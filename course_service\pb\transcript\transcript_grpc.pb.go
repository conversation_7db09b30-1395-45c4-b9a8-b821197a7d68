// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.5.1
// - protoc             v5.28.2
// source: pb/transcript/transcript.proto

package transcript

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.64.0 or later.
const _ = grpc.SupportPackageIsVersion9

const (
	TranscriptService_CreateDegree_FullMethodName             = "/transcript.TranscriptService/CreateDegree"
	TranscriptService_GetDegree_FullMethodName                = "/transcript.TranscriptService/GetDegree"
	TranscriptService_ListDegrees_FullMethodName              = "/transcript.TranscriptService/ListDegrees"
	TranscriptService_CreateTranscript_FullMethodName         = "/transcript.TranscriptService/CreateTranscript"
	TranscriptService_GetTranscript_FullMethodName            = "/transcript.TranscriptService/GetTranscript"
	TranscriptService_AddTranscriptEntry_FullMethodName       = "/transcript.TranscriptService/AddTranscriptEntry"
	TranscriptService_GetTranscriptEntries_FullMethodName     = "/transcript.TranscriptService/GetTranscriptEntries"
	TranscriptService_UpdateGPA_FullMethodName                = "/transcript.TranscriptService/UpdateGPA"
	TranscriptService_CalculateGPA_FullMethodName             = "/transcript.TranscriptService/CalculateGPA"
	TranscriptService_CreateStudentDegree_FullMethodName      = "/transcript.TranscriptService/CreateStudentDegree"
	TranscriptService_GetStudentDegrees_FullMethodName        = "/transcript.TranscriptService/GetStudentDegrees"
	TranscriptService_UpdateDegreeStatus_FullMethodName       = "/transcript.TranscriptService/UpdateDegreeStatus"
	TranscriptService_GenerateTranscriptReport_FullMethodName = "/transcript.TranscriptService/GenerateTranscriptReport"
)

// TranscriptServiceClient is the client API for TranscriptService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
//
// Service definition
type TranscriptServiceClient interface {
	// Degree management
	CreateDegree(ctx context.Context, in *CreateDegreeRequest, opts ...grpc.CallOption) (*DegreeResponse, error)
	GetDegree(ctx context.Context, in *GetDegreeRequest, opts ...grpc.CallOption) (*DegreeResponse, error)
	ListDegrees(ctx context.Context, in *ListDegreesRequest, opts ...grpc.CallOption) (*ListDegreesResponse, error)
	// Transcript management
	CreateTranscript(ctx context.Context, in *CreateTranscriptRequest, opts ...grpc.CallOption) (*TranscriptResponse, error)
	GetTranscript(ctx context.Context, in *GetTranscriptRequest, opts ...grpc.CallOption) (*TranscriptResponse, error)
	AddTranscriptEntry(ctx context.Context, in *AddTranscriptEntryRequest, opts ...grpc.CallOption) (*TranscriptEntryResponse, error)
	GetTranscriptEntries(ctx context.Context, in *GetTranscriptEntriesRequest, opts ...grpc.CallOption) (*GetTranscriptEntriesResponse, error)
	UpdateGPA(ctx context.Context, in *UpdateGPARequest, opts ...grpc.CallOption) (*TranscriptResponse, error)
	CalculateGPA(ctx context.Context, in *CalculateGPARequest, opts ...grpc.CallOption) (*GPAResponse, error)
	// Student degree management
	CreateStudentDegree(ctx context.Context, in *CreateStudentDegreeRequest, opts ...grpc.CallOption) (*StudentDegreeResponse, error)
	GetStudentDegrees(ctx context.Context, in *GetStudentDegreesRequest, opts ...grpc.CallOption) (*GetStudentDegreesResponse, error)
	UpdateDegreeStatus(ctx context.Context, in *UpdateDegreeStatusRequest, opts ...grpc.CallOption) (*StudentDegreeResponse, error)
	// Reports
	GenerateTranscriptReport(ctx context.Context, in *GenerateTranscriptReportRequest, opts ...grpc.CallOption) (*TranscriptReport, error)
}

type transcriptServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewTranscriptServiceClient(cc grpc.ClientConnInterface) TranscriptServiceClient {
	return &transcriptServiceClient{cc}
}

func (c *transcriptServiceClient) CreateDegree(ctx context.Context, in *CreateDegreeRequest, opts ...grpc.CallOption) (*DegreeResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(DegreeResponse)
	err := c.cc.Invoke(ctx, TranscriptService_CreateDegree_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *transcriptServiceClient) GetDegree(ctx context.Context, in *GetDegreeRequest, opts ...grpc.CallOption) (*DegreeResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(DegreeResponse)
	err := c.cc.Invoke(ctx, TranscriptService_GetDegree_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *transcriptServiceClient) ListDegrees(ctx context.Context, in *ListDegreesRequest, opts ...grpc.CallOption) (*ListDegreesResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(ListDegreesResponse)
	err := c.cc.Invoke(ctx, TranscriptService_ListDegrees_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *transcriptServiceClient) CreateTranscript(ctx context.Context, in *CreateTranscriptRequest, opts ...grpc.CallOption) (*TranscriptResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(TranscriptResponse)
	err := c.cc.Invoke(ctx, TranscriptService_CreateTranscript_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *transcriptServiceClient) GetTranscript(ctx context.Context, in *GetTranscriptRequest, opts ...grpc.CallOption) (*TranscriptResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(TranscriptResponse)
	err := c.cc.Invoke(ctx, TranscriptService_GetTranscript_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *transcriptServiceClient) AddTranscriptEntry(ctx context.Context, in *AddTranscriptEntryRequest, opts ...grpc.CallOption) (*TranscriptEntryResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(TranscriptEntryResponse)
	err := c.cc.Invoke(ctx, TranscriptService_AddTranscriptEntry_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *transcriptServiceClient) GetTranscriptEntries(ctx context.Context, in *GetTranscriptEntriesRequest, opts ...grpc.CallOption) (*GetTranscriptEntriesResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(GetTranscriptEntriesResponse)
	err := c.cc.Invoke(ctx, TranscriptService_GetTranscriptEntries_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *transcriptServiceClient) UpdateGPA(ctx context.Context, in *UpdateGPARequest, opts ...grpc.CallOption) (*TranscriptResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(TranscriptResponse)
	err := c.cc.Invoke(ctx, TranscriptService_UpdateGPA_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *transcriptServiceClient) CalculateGPA(ctx context.Context, in *CalculateGPARequest, opts ...grpc.CallOption) (*GPAResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(GPAResponse)
	err := c.cc.Invoke(ctx, TranscriptService_CalculateGPA_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *transcriptServiceClient) CreateStudentDegree(ctx context.Context, in *CreateStudentDegreeRequest, opts ...grpc.CallOption) (*StudentDegreeResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(StudentDegreeResponse)
	err := c.cc.Invoke(ctx, TranscriptService_CreateStudentDegree_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *transcriptServiceClient) GetStudentDegrees(ctx context.Context, in *GetStudentDegreesRequest, opts ...grpc.CallOption) (*GetStudentDegreesResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(GetStudentDegreesResponse)
	err := c.cc.Invoke(ctx, TranscriptService_GetStudentDegrees_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *transcriptServiceClient) UpdateDegreeStatus(ctx context.Context, in *UpdateDegreeStatusRequest, opts ...grpc.CallOption) (*StudentDegreeResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(StudentDegreeResponse)
	err := c.cc.Invoke(ctx, TranscriptService_UpdateDegreeStatus_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *transcriptServiceClient) GenerateTranscriptReport(ctx context.Context, in *GenerateTranscriptReportRequest, opts ...grpc.CallOption) (*TranscriptReport, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(TranscriptReport)
	err := c.cc.Invoke(ctx, TranscriptService_GenerateTranscriptReport_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// TranscriptServiceServer is the server API for TranscriptService service.
// All implementations must embed UnimplementedTranscriptServiceServer
// for forward compatibility.
//
// Service definition
type TranscriptServiceServer interface {
	// Degree management
	CreateDegree(context.Context, *CreateDegreeRequest) (*DegreeResponse, error)
	GetDegree(context.Context, *GetDegreeRequest) (*DegreeResponse, error)
	ListDegrees(context.Context, *ListDegreesRequest) (*ListDegreesResponse, error)
	// Transcript management
	CreateTranscript(context.Context, *CreateTranscriptRequest) (*TranscriptResponse, error)
	GetTranscript(context.Context, *GetTranscriptRequest) (*TranscriptResponse, error)
	AddTranscriptEntry(context.Context, *AddTranscriptEntryRequest) (*TranscriptEntryResponse, error)
	GetTranscriptEntries(context.Context, *GetTranscriptEntriesRequest) (*GetTranscriptEntriesResponse, error)
	UpdateGPA(context.Context, *UpdateGPARequest) (*TranscriptResponse, error)
	CalculateGPA(context.Context, *CalculateGPARequest) (*GPAResponse, error)
	// Student degree management
	CreateStudentDegree(context.Context, *CreateStudentDegreeRequest) (*StudentDegreeResponse, error)
	GetStudentDegrees(context.Context, *GetStudentDegreesRequest) (*GetStudentDegreesResponse, error)
	UpdateDegreeStatus(context.Context, *UpdateDegreeStatusRequest) (*StudentDegreeResponse, error)
	// Reports
	GenerateTranscriptReport(context.Context, *GenerateTranscriptReportRequest) (*TranscriptReport, error)
	mustEmbedUnimplementedTranscriptServiceServer()
}

// UnimplementedTranscriptServiceServer must be embedded to have
// forward compatible implementations.
//
// NOTE: this should be embedded by value instead of pointer to avoid a nil
// pointer dereference when methods are called.
type UnimplementedTranscriptServiceServer struct{}

func (UnimplementedTranscriptServiceServer) CreateDegree(context.Context, *CreateDegreeRequest) (*DegreeResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CreateDegree not implemented")
}
func (UnimplementedTranscriptServiceServer) GetDegree(context.Context, *GetDegreeRequest) (*DegreeResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetDegree not implemented")
}
func (UnimplementedTranscriptServiceServer) ListDegrees(context.Context, *ListDegreesRequest) (*ListDegreesResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListDegrees not implemented")
}
func (UnimplementedTranscriptServiceServer) CreateTranscript(context.Context, *CreateTranscriptRequest) (*TranscriptResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CreateTranscript not implemented")
}
func (UnimplementedTranscriptServiceServer) GetTranscript(context.Context, *GetTranscriptRequest) (*TranscriptResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetTranscript not implemented")
}
func (UnimplementedTranscriptServiceServer) AddTranscriptEntry(context.Context, *AddTranscriptEntryRequest) (*TranscriptEntryResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method AddTranscriptEntry not implemented")
}
func (UnimplementedTranscriptServiceServer) GetTranscriptEntries(context.Context, *GetTranscriptEntriesRequest) (*GetTranscriptEntriesResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetTranscriptEntries not implemented")
}
func (UnimplementedTranscriptServiceServer) UpdateGPA(context.Context, *UpdateGPARequest) (*TranscriptResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateGPA not implemented")
}
func (UnimplementedTranscriptServiceServer) CalculateGPA(context.Context, *CalculateGPARequest) (*GPAResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CalculateGPA not implemented")
}
func (UnimplementedTranscriptServiceServer) CreateStudentDegree(context.Context, *CreateStudentDegreeRequest) (*StudentDegreeResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CreateStudentDegree not implemented")
}
func (UnimplementedTranscriptServiceServer) GetStudentDegrees(context.Context, *GetStudentDegreesRequest) (*GetStudentDegreesResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetStudentDegrees not implemented")
}
func (UnimplementedTranscriptServiceServer) UpdateDegreeStatus(context.Context, *UpdateDegreeStatusRequest) (*StudentDegreeResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateDegreeStatus not implemented")
}
func (UnimplementedTranscriptServiceServer) GenerateTranscriptReport(context.Context, *GenerateTranscriptReportRequest) (*TranscriptReport, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GenerateTranscriptReport not implemented")
}
func (UnimplementedTranscriptServiceServer) mustEmbedUnimplementedTranscriptServiceServer() {}
func (UnimplementedTranscriptServiceServer) testEmbeddedByValue()                           {}

// UnsafeTranscriptServiceServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to TranscriptServiceServer will
// result in compilation errors.
type UnsafeTranscriptServiceServer interface {
	mustEmbedUnimplementedTranscriptServiceServer()
}

func RegisterTranscriptServiceServer(s grpc.ServiceRegistrar, srv TranscriptServiceServer) {
	// If the following call pancis, it indicates UnimplementedTranscriptServiceServer was
	// embedded by pointer and is nil.  This will cause panics if an
	// unimplemented method is ever invoked, so we test this at initialization
	// time to prevent it from happening at runtime later due to I/O.
	if t, ok := srv.(interface{ testEmbeddedByValue() }); ok {
		t.testEmbeddedByValue()
	}
	s.RegisterService(&TranscriptService_ServiceDesc, srv)
}

func _TranscriptService_CreateDegree_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CreateDegreeRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(TranscriptServiceServer).CreateDegree(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: TranscriptService_CreateDegree_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(TranscriptServiceServer).CreateDegree(ctx, req.(*CreateDegreeRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _TranscriptService_GetDegree_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetDegreeRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(TranscriptServiceServer).GetDegree(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: TranscriptService_GetDegree_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(TranscriptServiceServer).GetDegree(ctx, req.(*GetDegreeRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _TranscriptService_ListDegrees_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListDegreesRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(TranscriptServiceServer).ListDegrees(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: TranscriptService_ListDegrees_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(TranscriptServiceServer).ListDegrees(ctx, req.(*ListDegreesRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _TranscriptService_CreateTranscript_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CreateTranscriptRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(TranscriptServiceServer).CreateTranscript(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: TranscriptService_CreateTranscript_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(TranscriptServiceServer).CreateTranscript(ctx, req.(*CreateTranscriptRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _TranscriptService_GetTranscript_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetTranscriptRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(TranscriptServiceServer).GetTranscript(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: TranscriptService_GetTranscript_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(TranscriptServiceServer).GetTranscript(ctx, req.(*GetTranscriptRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _TranscriptService_AddTranscriptEntry_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(AddTranscriptEntryRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(TranscriptServiceServer).AddTranscriptEntry(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: TranscriptService_AddTranscriptEntry_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(TranscriptServiceServer).AddTranscriptEntry(ctx, req.(*AddTranscriptEntryRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _TranscriptService_GetTranscriptEntries_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetTranscriptEntriesRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(TranscriptServiceServer).GetTranscriptEntries(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: TranscriptService_GetTranscriptEntries_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(TranscriptServiceServer).GetTranscriptEntries(ctx, req.(*GetTranscriptEntriesRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _TranscriptService_UpdateGPA_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateGPARequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(TranscriptServiceServer).UpdateGPA(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: TranscriptService_UpdateGPA_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(TranscriptServiceServer).UpdateGPA(ctx, req.(*UpdateGPARequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _TranscriptService_CalculateGPA_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CalculateGPARequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(TranscriptServiceServer).CalculateGPA(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: TranscriptService_CalculateGPA_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(TranscriptServiceServer).CalculateGPA(ctx, req.(*CalculateGPARequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _TranscriptService_CreateStudentDegree_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CreateStudentDegreeRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(TranscriptServiceServer).CreateStudentDegree(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: TranscriptService_CreateStudentDegree_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(TranscriptServiceServer).CreateStudentDegree(ctx, req.(*CreateStudentDegreeRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _TranscriptService_GetStudentDegrees_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetStudentDegreesRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(TranscriptServiceServer).GetStudentDegrees(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: TranscriptService_GetStudentDegrees_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(TranscriptServiceServer).GetStudentDegrees(ctx, req.(*GetStudentDegreesRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _TranscriptService_UpdateDegreeStatus_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateDegreeStatusRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(TranscriptServiceServer).UpdateDegreeStatus(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: TranscriptService_UpdateDegreeStatus_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(TranscriptServiceServer).UpdateDegreeStatus(ctx, req.(*UpdateDegreeStatusRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _TranscriptService_GenerateTranscriptReport_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GenerateTranscriptReportRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(TranscriptServiceServer).GenerateTranscriptReport(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: TranscriptService_GenerateTranscriptReport_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(TranscriptServiceServer).GenerateTranscriptReport(ctx, req.(*GenerateTranscriptReportRequest))
	}
	return interceptor(ctx, in, info, handler)
}

// TranscriptService_ServiceDesc is the grpc.ServiceDesc for TranscriptService service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var TranscriptService_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "transcript.TranscriptService",
	HandlerType: (*TranscriptServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "CreateDegree",
			Handler:    _TranscriptService_CreateDegree_Handler,
		},
		{
			MethodName: "GetDegree",
			Handler:    _TranscriptService_GetDegree_Handler,
		},
		{
			MethodName: "ListDegrees",
			Handler:    _TranscriptService_ListDegrees_Handler,
		},
		{
			MethodName: "CreateTranscript",
			Handler:    _TranscriptService_CreateTranscript_Handler,
		},
		{
			MethodName: "GetTranscript",
			Handler:    _TranscriptService_GetTranscript_Handler,
		},
		{
			MethodName: "AddTranscriptEntry",
			Handler:    _TranscriptService_AddTranscriptEntry_Handler,
		},
		{
			MethodName: "GetTranscriptEntries",
			Handler:    _TranscriptService_GetTranscriptEntries_Handler,
		},
		{
			MethodName: "UpdateGPA",
			Handler:    _TranscriptService_UpdateGPA_Handler,
		},
		{
			MethodName: "CalculateGPA",
			Handler:    _TranscriptService_CalculateGPA_Handler,
		},
		{
			MethodName: "CreateStudentDegree",
			Handler:    _TranscriptService_CreateStudentDegree_Handler,
		},
		{
			MethodName: "GetStudentDegrees",
			Handler:    _TranscriptService_GetStudentDegrees_Handler,
		},
		{
			MethodName: "UpdateDegreeStatus",
			Handler:    _TranscriptService_UpdateDegreeStatus_Handler,
		},
		{
			MethodName: "GenerateTranscriptReport",
			Handler:    _TranscriptService_GenerateTranscriptReport_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "pb/transcript/transcript.proto",
}
