package transcript

import (
	"context"
	"fmt"

	"github.com/olzzhas/edunite-server/course_service/internal/database"
	transcriptpb "github.com/olzzhas/edunite-server/course_service/pb/transcript"
	"github.com/olzzhas/edunite-server/course_service/pkg/validator"
	"google.golang.org/grpc/codes"
	"google.golang.org/grpc/status"
	"google.golang.org/protobuf/types/known/timestamppb"
)

// Service represents the transcript service
type Service struct {
	transcriptpb.UnimplementedTranscriptServiceServer
	repo database.TranscriptRepository
}

// NewService creates a new transcript service
func NewService(repo database.TranscriptRepository) *Service {
	return &Service{
		repo: repo,
	}
}

// Helper functions for converting between protobuf and database models

func degreeToPB(degree *database.Degree) *transcriptpb.Degree {
	if degree == nil {
		return nil
	}

	return &transcriptpb.Degree{
		Id:              degree.ID,
		Name:            degree.Name,
		Level:           stringToDegreeLevel(degree.Level),
		Description:     degree.Description,
		RequiredCredits: degree.RequiredCredits,
		MinGpa:          degree.MinGPA,
		CreatedAt:       timestamppb.New(degree.CreatedAt),
		UpdatedAt:       timestamppb.New(degree.UpdatedAt),
	}
}

func transcriptToPB(transcript *database.AcademicTranscript) *transcriptpb.AcademicTranscript {
	if transcript == nil {
		return nil
	}

	pb := &transcriptpb.AcademicTranscript{
		Id:                    transcript.ID,
		UserId:                transcript.UserID,
		CumulativeGpa:         transcript.CumulativeGPA,
		TotalCreditsAttempted: transcript.TotalCreditsAttempted,
		TotalCreditsEarned:    transcript.TotalCreditsEarned,
		AcademicStanding:      stringToAcademicStanding(transcript.AcademicStanding),
		CreatedAt:             timestamppb.New(transcript.CreatedAt),
		UpdatedAt:             timestamppb.New(transcript.UpdatedAt),
	}

	if transcript.DegreeID != nil {
		pb.DegreeId = *transcript.DegreeID
	}

	if transcript.GraduationDate != nil {
		pb.GraduationDate = timestamppb.New(*transcript.GraduationDate)
	}

	if transcript.Degree != nil {
		pb.Degree = degreeToPB(transcript.Degree)
	}

	return pb
}

func transcriptEntryToPB(entry *database.TranscriptEntry) *transcriptpb.TranscriptEntry {
	if entry == nil {
		return nil
	}

	pb := &transcriptpb.TranscriptEntry{
		Id:           entry.ID,
		TranscriptId: entry.TranscriptID,
		CourseId:     entry.CourseID,
		SemesterId:   entry.SemesterID,
		Credits:      entry.Credits,
		IsTransfer:   entry.IsTransfer,
		IsRepeated:   entry.IsRepeated,
		CreatedAt:    timestamppb.New(entry.CreatedAt),
		UpdatedAt:    timestamppb.New(entry.UpdatedAt),
		CourseTitle:  entry.CourseTitle,
		SemesterName: entry.SemesterName,
	}

	if entry.ThreadID != nil {
		pb.ThreadId = *entry.ThreadID
	}

	if entry.GradeLetter != nil {
		pb.GradeLetter = *entry.GradeLetter
	}

	if entry.GradeNumeric != nil {
		pb.GradeNumeric = *entry.GradeNumeric
	}

	if entry.GradePoints != nil {
		pb.GradePoints = *entry.GradePoints
	}

	if entry.CompletionDate != nil {
		pb.CompletionDate = timestamppb.New(*entry.CompletionDate)
	}

	return pb
}

func studentDegreeToPB(studentDegree *database.StudentDegree) *transcriptpb.StudentDegree {
	if studentDegree == nil {
		return nil
	}

	pb := &transcriptpb.StudentDegree{
		Id:        studentDegree.ID,
		UserId:    studentDegree.UserID,
		DegreeId:  studentDegree.DegreeID,
		Status:    stringToDegreeStatus(studentDegree.Status),
		StartDate: timestamppb.New(studentDegree.StartDate),
		CreatedAt: timestamppb.New(studentDegree.CreatedAt),
		UpdatedAt: timestamppb.New(studentDegree.UpdatedAt),
	}

	if studentDegree.ExpectedGraduationDate != nil {
		pb.ExpectedGraduationDate = timestamppb.New(*studentDegree.ExpectedGraduationDate)
	}

	if studentDegree.ActualGraduationDate != nil {
		pb.ActualGraduationDate = timestamppb.New(*studentDegree.ActualGraduationDate)
	}

	if studentDegree.FinalGPA != nil {
		pb.FinalGpa = *studentDegree.FinalGPA
	}

	if studentDegree.Degree != nil {
		pb.Degree = degreeToPB(studentDegree.Degree)
	}

	return pb
}

// Enum conversion functions
func stringToDegreeLevel(level string) transcriptpb.DegreeLevel {
	switch level {
	case "bachelor":
		return transcriptpb.DegreeLevel_BACHELOR
	case "master":
		return transcriptpb.DegreeLevel_MASTER
	case "phd":
		return transcriptpb.DegreeLevel_PHD
	case "certificate":
		return transcriptpb.DegreeLevel_CERTIFICATE
	case "diploma":
		return transcriptpb.DegreeLevel_DIPLOMA
	default:
		return transcriptpb.DegreeLevel_BACHELOR
	}
}

func degreeLevelToString(level transcriptpb.DegreeLevel) string {
	switch level {
	case transcriptpb.DegreeLevel_BACHELOR:
		return "bachelor"
	case transcriptpb.DegreeLevel_MASTER:
		return "master"
	case transcriptpb.DegreeLevel_PHD:
		return "phd"
	case transcriptpb.DegreeLevel_CERTIFICATE:
		return "certificate"
	case transcriptpb.DegreeLevel_DIPLOMA:
		return "diploma"
	default:
		return "bachelor"
	}
}

func stringToAcademicStanding(standing string) transcriptpb.AcademicStanding {
	switch standing {
	case "good_standing":
		return transcriptpb.AcademicStanding_GOOD_STANDING
	case "academic_warning":
		return transcriptpb.AcademicStanding_ACADEMIC_WARNING
	case "academic_probation":
		return transcriptpb.AcademicStanding_ACADEMIC_PROBATION
	case "academic_suspension":
		return transcriptpb.AcademicStanding_ACADEMIC_SUSPENSION
	case "dismissed":
		return transcriptpb.AcademicStanding_DISMISSED
	default:
		return transcriptpb.AcademicStanding_GOOD_STANDING
	}
}

func academicStandingToString(standing transcriptpb.AcademicStanding) string {
	switch standing {
	case transcriptpb.AcademicStanding_GOOD_STANDING:
		return "good_standing"
	case transcriptpb.AcademicStanding_ACADEMIC_WARNING:
		return "academic_warning"
	case transcriptpb.AcademicStanding_ACADEMIC_PROBATION:
		return "academic_probation"
	case transcriptpb.AcademicStanding_ACADEMIC_SUSPENSION:
		return "academic_suspension"
	case transcriptpb.AcademicStanding_DISMISSED:
		return "dismissed"
	default:
		return "good_standing"
	}
}

func stringToDegreeStatus(status string) transcriptpb.DegreeStatus {
	switch status {
	case "in_progress":
		return transcriptpb.DegreeStatus_IN_PROGRESS
	case "completed":
		return transcriptpb.DegreeStatus_COMPLETED
	case "withdrawn":
		return transcriptpb.DegreeStatus_WITHDRAWN
	case "transferred":
		return transcriptpb.DegreeStatus_TRANSFERRED
	default:
		return transcriptpb.DegreeStatus_IN_PROGRESS
	}
}

func degreeStatusToString(status transcriptpb.DegreeStatus) string {
	switch status {
	case transcriptpb.DegreeStatus_IN_PROGRESS:
		return "in_progress"
	case transcriptpb.DegreeStatus_COMPLETED:
		return "completed"
	case transcriptpb.DegreeStatus_WITHDRAWN:
		return "withdrawn"
	case transcriptpb.DegreeStatus_TRANSFERRED:
		return "transferred"
	default:
		return "in_progress"
	}
}

// Degree management methods
func (s *Service) CreateDegree(ctx context.Context, req *transcriptpb.CreateDegreeRequest) (*transcriptpb.DegreeResponse, error) {
	v := validator.New()
	v.Check(req.GetName() != "", "name", "must be provided")
	v.Check(len(req.GetName()) <= 255, "name", "max length is 255")
	v.Check(req.GetRequiredCredits() > 0, "required_credits", "must be > 0")
	v.Check(req.GetMinGpa() >= 0 && req.GetMinGpa() <= 4.0, "min_gpa", "must be between 0 and 4.0")

	if !v.Valid() {
		return nil, status.Errorf(codes.InvalidArgument, "validation failed: %v", v.Errors)
	}

	degree := &database.Degree{
		Name:            req.GetName(),
		Level:           degreeLevelToString(req.GetLevel()),
		Description:     req.GetDescription(),
		RequiredCredits: req.GetRequiredCredits(),
		MinGPA:          req.GetMinGpa(),
	}

	if err := s.repo.CreateDegree(ctx, degree); err != nil {
		return nil, status.Errorf(codes.Internal, "could not create degree: %v", err)
	}

	return &transcriptpb.DegreeResponse{
		Degree: degreeToPB(degree),
	}, nil
}

func (s *Service) GetDegree(ctx context.Context, req *transcriptpb.GetDegreeRequest) (*transcriptpb.DegreeResponse, error) {
	v := validator.New()
	v.Check(req.GetId() > 0, "id", "must be > 0")

	if !v.Valid() {
		return nil, status.Errorf(codes.InvalidArgument, "validation failed: %v", v.Errors)
	}

	degree, err := s.repo.GetDegree(ctx, req.GetId())
	if err != nil {
		if err == database.ErrDegreeNotFound {
			return nil, status.Errorf(codes.NotFound, "degree not found")
		}
		return nil, status.Errorf(codes.Internal, "could not get degree: %v", err)
	}

	return &transcriptpb.DegreeResponse{
		Degree: degreeToPB(degree),
	}, nil
}

func (s *Service) ListDegrees(ctx context.Context, req *transcriptpb.ListDegreesRequest) (*transcriptpb.ListDegreesResponse, error) {
	page := req.GetPage()
	pageSize := req.GetPageSize()

	if page <= 0 {
		page = 1
	}
	if pageSize <= 0 {
		pageSize = 10
	}
	if pageSize > 100 {
		pageSize = 100
	}

	offset := (page - 1) * pageSize
	level := degreeLevelToString(req.GetLevel())
	if req.GetLevel() == transcriptpb.DegreeLevel_BACHELOR && level == "bachelor" {
		level = "" // If default value, don't filter
	}

	degrees, totalCount, err := s.repo.ListDegrees(ctx, level, int(offset), int(pageSize))
	if err != nil {
		return nil, status.Errorf(codes.Internal, "could not list degrees: %v", err)
	}

	var pbDegrees []*transcriptpb.Degree
	for _, degree := range degrees {
		pbDegrees = append(pbDegrees, degreeToPB(degree))
	}

	return &transcriptpb.ListDegreesResponse{
		Degrees:    pbDegrees,
		TotalCount: int32(totalCount),
	}, nil
}

// Transcript management methods
func (s *Service) CreateTranscript(ctx context.Context, req *transcriptpb.CreateTranscriptRequest) (*transcriptpb.TranscriptResponse, error) {
	v := validator.New()
	v.Check(req.GetUserId() > 0, "user_id", "must be > 0")

	if req.GetDegreeId() > 0 {
		v.Check(req.GetDegreeId() > 0, "degree_id", "must be > 0")
	}

	if !v.Valid() {
		return nil, status.Errorf(codes.InvalidArgument, "validation failed: %v", v.Errors)
	}

	transcript := &database.AcademicTranscript{
		UserID:                req.GetUserId(),
		CumulativeGPA:         0.0,
		TotalCreditsAttempted: 0,
		TotalCreditsEarned:    0,
		AcademicStanding:      "good_standing",
	}

	if req.GetDegreeId() > 0 {
		transcript.DegreeID = &req.DegreeId
	}

	if err := s.repo.CreateTranscript(ctx, transcript); err != nil {
		return nil, status.Errorf(codes.Internal, "could not create transcript: %v", err)
	}

	return &transcriptpb.TranscriptResponse{
		Transcript: transcriptToPB(transcript),
	}, nil
}

func (s *Service) GetTranscript(ctx context.Context, req *transcriptpb.GetTranscriptRequest) (*transcriptpb.TranscriptResponse, error) {
	v := validator.New()
	v.Check(req.GetUserId() > 0, "user_id", "must be > 0")

	if !v.Valid() {
		return nil, status.Errorf(codes.InvalidArgument, "validation failed: %v", v.Errors)
	}

	var degreeID *int64
	if req.GetDegreeId() > 0 {
		degreeID = &req.DegreeId
	}

	transcript, err := s.repo.GetTranscript(ctx, req.GetUserId(), degreeID)
	if err != nil {
		if err == database.ErrTranscriptNotFound {
			return nil, status.Errorf(codes.NotFound, "transcript not found")
		}
		return nil, status.Errorf(codes.Internal, "could not get transcript: %v", err)
	}

	return &transcriptpb.TranscriptResponse{
		Transcript: transcriptToPB(transcript),
	}, nil
}

func (s *Service) AddTranscriptEntry(ctx context.Context, req *transcriptpb.AddTranscriptEntryRequest) (*transcriptpb.TranscriptEntryResponse, error) {
	v := validator.New()
	v.Check(req.GetTranscriptId() > 0, "transcript_id", "must be > 0")
	v.Check(req.GetCourseId() > 0, "course_id", "must be > 0")
	v.Check(req.GetSemesterId() > 0, "semester_id", "must be > 0")
	v.Check(req.GetCredits() > 0, "credits", "must be > 0")

	if req.GetGradePoints() != 0 {
		v.Check(req.GetGradePoints() >= 0 && req.GetGradePoints() <= 4.0, "grade_points", "must be between 0 and 4.0")
	}

	if req.GetGradeNumeric() != 0 {
		v.Check(req.GetGradeNumeric() >= 0 && req.GetGradeNumeric() <= 100, "grade_numeric", "must be between 0 and 100")
	}

	if !v.Valid() {
		return nil, status.Errorf(codes.InvalidArgument, "validation failed: %v", v.Errors)
	}

	entry := &database.TranscriptEntry{
		TranscriptID: req.GetTranscriptId(),
		CourseID:     req.GetCourseId(),
		SemesterID:   req.GetSemesterId(),
		Credits:      req.GetCredits(),
		IsTransfer:   req.GetIsTransfer(),
		IsRepeated:   req.GetIsRepeated(),
	}

	if req.GetThreadId() > 0 {
		entry.ThreadID = &req.ThreadId
	}

	if req.GetGradeLetter() != "" {
		entry.GradeLetter = &req.GradeLetter
	}

	if req.GetGradeNumeric() != 0 {
		entry.GradeNumeric = &req.GradeNumeric
	}

	if req.GetGradePoints() != 0 {
		entry.GradePoints = &req.GradePoints
	}

	if req.GetCompletionDate() != nil {
		completionDate := req.GetCompletionDate().AsTime()
		entry.CompletionDate = &completionDate
	}

	if err := s.repo.AddTranscriptEntry(ctx, entry); err != nil {
		return nil, status.Errorf(codes.Internal, "could not add transcript entry: %v", err)
	}

	// Update GPA after adding entry
	if err := s.repo.UpdateGPA(ctx, req.GetTranscriptId()); err != nil {
		// Log error but don't fail the request
		fmt.Printf("Warning: could not update GPA for transcript %d: %v\n", req.GetTranscriptId(), err)
	}

	return &transcriptpb.TranscriptEntryResponse{
		Entry: transcriptEntryToPB(entry),
	}, nil
}

func (s *Service) GetTranscriptEntries(ctx context.Context, req *transcriptpb.GetTranscriptEntriesRequest) (*transcriptpb.GetTranscriptEntriesResponse, error) {
	v := validator.New()
	v.Check(req.GetTranscriptId() > 0, "transcript_id", "must be > 0")

	if !v.Valid() {
		return nil, status.Errorf(codes.InvalidArgument, "validation failed: %v", v.Errors)
	}

	var semesterID *int64
	if req.GetSemesterId() > 0 {
		semesterID = &req.SemesterId
	}

	entries, err := s.repo.GetTranscriptEntries(ctx, req.GetTranscriptId(), semesterID)
	if err != nil {
		return nil, status.Errorf(codes.Internal, "could not get transcript entries: %v", err)
	}

	var pbEntries []*transcriptpb.TranscriptEntry
	for _, entry := range entries {
		pbEntries = append(pbEntries, transcriptEntryToPB(entry))
	}

	return &transcriptpb.GetTranscriptEntriesResponse{
		Entries: pbEntries,
	}, nil
}

func (s *Service) UpdateGPA(ctx context.Context, req *transcriptpb.UpdateGPARequest) (*transcriptpb.TranscriptResponse, error) {
	v := validator.New()
	v.Check(req.GetTranscriptId() > 0, "transcript_id", "must be > 0")

	if !v.Valid() {
		return nil, status.Errorf(codes.InvalidArgument, "validation failed: %v", v.Errors)
	}

	if err := s.repo.UpdateGPA(ctx, req.GetTranscriptId()); err != nil {
		return nil, status.Errorf(codes.Internal, "could not update GPA: %v", err)
	}

	// Get updated transcript
	transcript, err := s.repo.GetTranscriptByID(ctx, req.GetTranscriptId())
	if err != nil {
		return nil, status.Errorf(codes.Internal, "could not get updated transcript: %v", err)
	}

	return &transcriptpb.TranscriptResponse{
		Transcript: transcriptToPB(transcript),
	}, nil
}

func (s *Service) CalculateGPA(ctx context.Context, req *transcriptpb.CalculateGPARequest) (*transcriptpb.GPAResponse, error) {
	v := validator.New()
	v.Check(req.GetUserId() > 0, "user_id", "must be > 0")

	if !v.Valid() {
		return nil, status.Errorf(codes.InvalidArgument, "validation failed: %v", v.Errors)
	}

	var degreeID *int64
	if req.GetDegreeId() > 0 {
		degreeID = &req.DegreeId
	}

	// Get transcript first
	transcript, err := s.repo.GetTranscript(ctx, req.GetUserId(), degreeID)
	if err != nil {
		if err == database.ErrTranscriptNotFound {
			return nil, status.Errorf(codes.NotFound, "transcript not found")
		}
		return nil, status.Errorf(codes.Internal, "could not get transcript: %v", err)
	}

	gpa, creditsAttempted, creditsEarned, err := s.repo.CalculateGPA(ctx, transcript.ID)
	if err != nil {
		return nil, status.Errorf(codes.Internal, "could not calculate GPA: %v", err)
	}

	return &transcriptpb.GPAResponse{
		Gpa:                   gpa,
		TotalCreditsAttempted: creditsAttempted,
		TotalCreditsEarned:    creditsEarned,
	}, nil
}

// Student degree management methods
func (s *Service) CreateStudentDegree(ctx context.Context, req *transcriptpb.CreateStudentDegreeRequest) (*transcriptpb.StudentDegreeResponse, error) {
	v := validator.New()
	v.Check(req.GetUserId() > 0, "user_id", "must be > 0")
	v.Check(req.GetDegreeId() > 0, "degree_id", "must be > 0")

	if !v.Valid() {
		return nil, status.Errorf(codes.InvalidArgument, "validation failed: %v", v.Errors)
	}

	studentDegree := &database.StudentDegree{
		UserID:   req.GetUserId(),
		DegreeID: req.GetDegreeId(),
		Status:   "in_progress",
	}

	if req.GetStartDate() != nil {
		studentDegree.StartDate = req.GetStartDate().AsTime()
	}

	if req.GetExpectedGraduationDate() != nil {
		expectedDate := req.GetExpectedGraduationDate().AsTime()
		studentDegree.ExpectedGraduationDate = &expectedDate
	}

	if err := s.repo.CreateStudentDegree(ctx, studentDegree); err != nil {
		return nil, status.Errorf(codes.Internal, "could not create student degree: %v", err)
	}

	return &transcriptpb.StudentDegreeResponse{
		StudentDegree: studentDegreeToPB(studentDegree),
	}, nil
}

func (s *Service) GetStudentDegrees(ctx context.Context, req *transcriptpb.GetStudentDegreesRequest) (*transcriptpb.GetStudentDegreesResponse, error) {
	v := validator.New()
	v.Check(req.GetUserId() > 0, "user_id", "must be > 0")

	if !v.Valid() {
		return nil, status.Errorf(codes.InvalidArgument, "validation failed: %v", v.Errors)
	}

	studentDegrees, err := s.repo.GetStudentDegrees(ctx, req.GetUserId())
	if err != nil {
		return nil, status.Errorf(codes.Internal, "could not get student degrees: %v", err)
	}

	var pbStudentDegrees []*transcriptpb.StudentDegree
	for _, studentDegree := range studentDegrees {
		pbStudentDegrees = append(pbStudentDegrees, studentDegreeToPB(studentDegree))
	}

	return &transcriptpb.GetStudentDegreesResponse{
		StudentDegrees: pbStudentDegrees,
	}, nil
}

func (s *Service) UpdateDegreeStatus(ctx context.Context, req *transcriptpb.UpdateDegreeStatusRequest) (*transcriptpb.StudentDegreeResponse, error) {
	v := validator.New()
	v.Check(req.GetStudentDegreeId() > 0, "student_degree_id", "must be > 0")

	if req.GetStatus() == transcriptpb.DegreeStatus_COMPLETED {
		v.Check(req.GetFinalGpa() >= 0 && req.GetFinalGpa() <= 4.0, "final_gpa", "must be between 0 and 4.0 when status is completed")
		v.Check(req.GetActualGraduationDate() != nil, "actual_graduation_date", "must be provided when status is completed")
	}

	if !v.Valid() {
		return nil, status.Errorf(codes.InvalidArgument, "validation failed: %v", v.Errors)
	}

	// Get existing student degree
	studentDegree, err := s.repo.GetStudentDegree(ctx, req.GetStudentDegreeId())
	if err != nil {
		if err == database.ErrStudentDegreeNotFound {
			return nil, status.Errorf(codes.NotFound, "student degree not found")
		}
		return nil, status.Errorf(codes.Internal, "could not get student degree: %v", err)
	}

	// Update fields
	studentDegree.Status = degreeStatusToString(req.GetStatus())

	if req.GetActualGraduationDate() != nil {
		actualDate := req.GetActualGraduationDate().AsTime()
		studentDegree.ActualGraduationDate = &actualDate
	}

	if req.GetFinalGpa() != 0 {
		studentDegree.FinalGPA = &req.FinalGpa
	}

	if err := s.repo.UpdateStudentDegree(ctx, studentDegree); err != nil {
		return nil, status.Errorf(codes.Internal, "could not update student degree: %v", err)
	}

	return &transcriptpb.StudentDegreeResponse{
		StudentDegree: studentDegreeToPB(studentDegree),
	}, nil
}

// Reports
func (s *Service) GenerateTranscriptReport(ctx context.Context, req *transcriptpb.GenerateTranscriptReportRequest) (*transcriptpb.TranscriptReport, error) {
	v := validator.New()
	v.Check(req.GetUserId() > 0, "user_id", "must be > 0")

	if !v.Valid() {
		return nil, status.Errorf(codes.InvalidArgument, "validation failed: %v", v.Errors)
	}

	var degreeID *int64
	if req.GetDegreeId() > 0 {
		degreeID = &req.DegreeId
	}

	// Get transcript
	transcript, err := s.repo.GetTranscript(ctx, req.GetUserId(), degreeID)
	if err != nil {
		if err == database.ErrTranscriptNotFound {
			return nil, status.Errorf(codes.NotFound, "transcript not found")
		}
		return nil, status.Errorf(codes.Internal, "could not get transcript: %v", err)
	}

	// Get transcript entries
	entries, err := s.repo.GetTranscriptEntries(ctx, transcript.ID, nil)
	if err != nil {
		return nil, status.Errorf(codes.Internal, "could not get transcript entries: %v", err)
	}

	// Filter entries based on request parameters
	var filteredEntries []*database.TranscriptEntry
	for _, entry := range entries {
		if !req.GetIncludeTransferCredits() && entry.IsTransfer {
			continue
		}
		if !req.GetIncludeRepeatedCourses() && entry.IsRepeated {
			continue
		}
		filteredEntries = append(filteredEntries, entry)
	}

	// Get student degree if degree ID is specified
	var studentDegree *database.StudentDegree
	if degreeID != nil {
		studentDegrees, err := s.repo.GetStudentDegrees(ctx, req.GetUserId())
		if err != nil {
			return nil, status.Errorf(codes.Internal, "could not get student degrees: %v", err)
		}

		for _, sd := range studentDegrees {
			if sd.DegreeID == *degreeID {
				studentDegree = sd
				break
			}
		}
	}

	// Calculate current semester GPA (simplified - using most recent semester)
	var semesterGPA float64
	if len(filteredEntries) > 0 {
		// Get the most recent semester
		recentSemesterID := filteredEntries[0].SemesterID
		var semesterEntries []*database.TranscriptEntry
		for _, entry := range filteredEntries {
			if entry.SemesterID == recentSemesterID {
				semesterEntries = append(semesterEntries, entry)
			}
		}

		// Calculate GPA for this semester
		var totalGradePoints, totalCredits float64
		for _, entry := range semesterEntries {
			if entry.GradePoints != nil {
				totalGradePoints += *entry.GradePoints * float64(entry.Credits)
				totalCredits += float64(entry.Credits)
			}
		}
		if totalCredits > 0 {
			semesterGPA = totalGradePoints / totalCredits
		}
	}

	// Generate academic standing description
	standingDescription := getAcademicStandingDescription(transcript.AcademicStanding)

	// Convert to protobuf
	var pbEntries []*transcriptpb.TranscriptEntry
	for _, entry := range filteredEntries {
		pbEntries = append(pbEntries, transcriptEntryToPB(entry))
	}

	report := &transcriptpb.TranscriptReport{
		Transcript:                  transcriptToPB(transcript),
		Entries:                     pbEntries,
		SemesterGpa:                 semesterGPA,
		AcademicStandingDescription: standingDescription,
	}

	if studentDegree != nil {
		report.StudentDegree = studentDegreeToPB(studentDegree)
	}

	return report, nil
}

func getAcademicStandingDescription(standing string) string {
	switch standing {
	case "good_standing":
		return "Student is in good academic standing"
	case "academic_warning":
		return "Student is on academic warning - GPA below required threshold"
	case "academic_probation":
		return "Student is on academic probation - immediate improvement required"
	case "academic_suspension":
		return "Student is on academic suspension - enrollment suspended"
	case "dismissed":
		return "Student has been academically dismissed"
	default:
		return "Academic standing status unknown"
	}
}
