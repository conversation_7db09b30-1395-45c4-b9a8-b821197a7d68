package main

import (
	"log"
	"net"
	"os"
	"os/signal"
	"syscall"

	"github.com/olzzhas/edunite-server/course_service/internal/config"
	"github.com/olzzhas/edunite-server/course_service/internal/database"
	"github.com/olzzhas/edunite-server/course_service/internal/query/assignmentquery"
	"github.com/olzzhas/edunite-server/course_service/internal/query/threadquery"
	"github.com/olzzhas/edunite-server/course_service/internal/service/assignment"
	"github.com/olzzhas/edunite-server/course_service/internal/service/attendance"
	"github.com/olzzhas/edunite-server/course_service/internal/service/course"
	"github.com/olzzhas/edunite-server/course_service/internal/service/semester"
	"github.com/olzzhas/edunite-server/course_service/internal/service/thread"
	"github.com/olzzhas/edunite-server/course_service/internal/service/transcript"
	assignmentpb "github.com/olzzhas/edunite-server/course_service/pb/assignment"
	attendancepb "github.com/olzzhas/edunite-server/course_service/pb/attendance"
	coursepb "github.com/olzzhas/edunite-server/course_service/pb/course"
	semesterpb "github.com/olzzhas/edunite-server/course_service/pb/semester"
	threadpb "github.com/olzzhas/edunite-server/course_service/pb/thread"
	transcriptpb "github.com/olzzhas/edunite-server/course_service/pb/transcript"
	"google.golang.org/grpc"
	"google.golang.org/grpc/reflection"
)

func main() {
	// Загружаем конфигурацию
	cfg := config.LoadConfig()

	// Подключаемся к базе данных CockroachDB без сертификатов
	db := database.ConnectDBCockroach(cfg)
	defer db.Close()

	// Создаём репозитории
	courseRepo := database.NewCourseRepository(db)
	threadRepo := database.NewThreadRepository(db)
	semesterRepo := database.NewSemesterRepository(db)
	assignmentRepo := database.NewAssignmentRepository(db)
	assignmentGroupRepo := database.NewAssignmentGroupRepository(db)
	assignmentAttachmentRepo := database.NewAssignmentAttachmentRepository(db)
	assignmentSubmissionRepo := database.NewAssignmentSubmissionRepository(db)
	weekRepo := database.NewWeekRepository(db)
	scheduleRepo := database.NewThreadScheduleRepository(db)
	locationRepo := database.NewLocationRepository(db)
	attendanceRepo := database.NewAttendanceRepository(db)
	transcriptRepo := database.NewTranscriptRepository(db)
	threadQuery := threadquery.New(db)
	assignmentQuery := assignmentquery.NewRepo(db)

	// Создаём gRPC-сервисы
	courseService := course.NewCourseService(courseRepo)
	threadService := thread.NewThreadService(threadRepo, weekRepo, scheduleRepo, locationRepo, *threadQuery, courseRepo)
	semesterService := semester.NewSemesterService(semesterRepo)
	assignmentService := assignment.NewAssignmentService(assignmentRepo, assignmentGroupRepo, assignmentAttachmentRepo, assignmentSubmissionRepo, *assignmentQuery)
	attendanceService := attendance.NewAttendanceService(attendanceRepo)
	transcriptService := transcript.NewService(transcriptRepo)

	// Настраиваем gRPC сервер
	grpcServer := grpc.NewServer()

	// Регистрируем сервисы внутри нашего gRPC-сервера.
	// Обратите внимание, что все они «расширяют» pb.UnimplementedCourseServiceServer,
	// но фактически регистрируются как один сервис CourseService.
	// Это возможно потому, что вы в .proto объявили всё в одном service CourseService.
	// Для отдельного сервиса в .proto нужны отдельные сервисные интерфейсы.
	coursepb.RegisterCourseServiceServer(grpcServer, courseService)
	threadpb.RegisterThreadServiceServer(grpcServer, threadService)
	semesterpb.RegisterSemesterServiceServer(grpcServer, semesterService)
	assignmentpb.RegisterAssignmentServiceServer(grpcServer, assignmentService)
	attendancepb.RegisterAttendanceServiceServer(grpcServer, attendanceService)
	transcriptpb.RegisterTranscriptServiceServer(grpcServer, transcriptService)

	// Включаем reflection, чтобы пользоваться утилитами вроде grpc_cli
	reflection.Register(grpcServer)

	// Запуск слушателя на порту 50051 (или любом другом)
	listener, err := net.Listen("tcp", ":50053")
	if err != nil {
		log.Fatalf("failed to listen on port 50053: %v", err)
	}
	log.Printf("gRPC server listening on :50053")

	// Запускаем gRPC-сервер в отдельном горутине
	go func() {
		if serveErr := grpcServer.Serve(listener); serveErr != nil {
			log.Fatalf("failed to serve gRPC: %v", serveErr)
		}
	}()

	// Ждём сигнала завершения (Ctrl+C или SIGTERM в Kubernetes)
	waitForShutdown(grpcServer)
	log.Println("Server gracefully stopped")
}

// waitForShutdown блокирует до получения системного сигнала и корректно останавливает gRPC-сервер.
func waitForShutdown(server *grpc.Server) {
	// Создаём канал, в который пойдут сигналы ОС
	quit := make(chan os.Signal, 1)
	signal.Notify(quit, os.Interrupt, syscall.SIGTERM)
	<-quit

	log.Println("Received shutdown signal, gracefully stopping gRPC server...")
	server.GracefulStop()
}
