// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.6
// 	protoc        v5.28.2
// source: pb/transcript/transcript.proto

package transcript

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	timestamppb "google.golang.org/protobuf/types/known/timestamppb"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// Enums
type AcademicStanding int32

const (
	AcademicStanding_GOOD_STANDING       AcademicStanding = 0
	AcademicStanding_ACADEMIC_WARNING    AcademicStanding = 1
	AcademicStanding_ACADEMIC_PROBATION  AcademicStanding = 2
	AcademicStanding_ACADEMIC_SUSPENSION AcademicStanding = 3
	AcademicStanding_DISMISSED           AcademicStanding = 4
)

// Enum value maps for AcademicStanding.
var (
	AcademicStanding_name = map[int32]string{
		0: "GOOD_STANDING",
		1: "ACADEMIC_WARNING",
		2: "ACADEMIC_PROBATION",
		3: "ACADEMIC_SUSPENSION",
		4: "DISMISSED",
	}
	AcademicStanding_value = map[string]int32{
		"GOOD_STANDING":       0,
		"ACADEMIC_WARNING":    1,
		"ACADEMIC_PROBATION":  2,
		"ACADEMIC_SUSPENSION": 3,
		"DISMISSED":           4,
	}
)

func (x AcademicStanding) Enum() *AcademicStanding {
	p := new(AcademicStanding)
	*p = x
	return p
}

func (x AcademicStanding) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (AcademicStanding) Descriptor() protoreflect.EnumDescriptor {
	return file_pb_transcript_transcript_proto_enumTypes[0].Descriptor()
}

func (AcademicStanding) Type() protoreflect.EnumType {
	return &file_pb_transcript_transcript_proto_enumTypes[0]
}

func (x AcademicStanding) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use AcademicStanding.Descriptor instead.
func (AcademicStanding) EnumDescriptor() ([]byte, []int) {
	return file_pb_transcript_transcript_proto_rawDescGZIP(), []int{0}
}

type DegreeLevel int32

const (
	DegreeLevel_BACHELOR    DegreeLevel = 0
	DegreeLevel_MASTER      DegreeLevel = 1
	DegreeLevel_PHD         DegreeLevel = 2
	DegreeLevel_CERTIFICATE DegreeLevel = 3
	DegreeLevel_DIPLOMA     DegreeLevel = 4
)

// Enum value maps for DegreeLevel.
var (
	DegreeLevel_name = map[int32]string{
		0: "BACHELOR",
		1: "MASTER",
		2: "PHD",
		3: "CERTIFICATE",
		4: "DIPLOMA",
	}
	DegreeLevel_value = map[string]int32{
		"BACHELOR":    0,
		"MASTER":      1,
		"PHD":         2,
		"CERTIFICATE": 3,
		"DIPLOMA":     4,
	}
)

func (x DegreeLevel) Enum() *DegreeLevel {
	p := new(DegreeLevel)
	*p = x
	return p
}

func (x DegreeLevel) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (DegreeLevel) Descriptor() protoreflect.EnumDescriptor {
	return file_pb_transcript_transcript_proto_enumTypes[1].Descriptor()
}

func (DegreeLevel) Type() protoreflect.EnumType {
	return &file_pb_transcript_transcript_proto_enumTypes[1]
}

func (x DegreeLevel) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use DegreeLevel.Descriptor instead.
func (DegreeLevel) EnumDescriptor() ([]byte, []int) {
	return file_pb_transcript_transcript_proto_rawDescGZIP(), []int{1}
}

type DegreeStatus int32

const (
	DegreeStatus_IN_PROGRESS DegreeStatus = 0
	DegreeStatus_COMPLETED   DegreeStatus = 1
	DegreeStatus_WITHDRAWN   DegreeStatus = 2
	DegreeStatus_TRANSFERRED DegreeStatus = 3
)

// Enum value maps for DegreeStatus.
var (
	DegreeStatus_name = map[int32]string{
		0: "IN_PROGRESS",
		1: "COMPLETED",
		2: "WITHDRAWN",
		3: "TRANSFERRED",
	}
	DegreeStatus_value = map[string]int32{
		"IN_PROGRESS": 0,
		"COMPLETED":   1,
		"WITHDRAWN":   2,
		"TRANSFERRED": 3,
	}
)

func (x DegreeStatus) Enum() *DegreeStatus {
	p := new(DegreeStatus)
	*p = x
	return p
}

func (x DegreeStatus) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (DegreeStatus) Descriptor() protoreflect.EnumDescriptor {
	return file_pb_transcript_transcript_proto_enumTypes[2].Descriptor()
}

func (DegreeStatus) Type() protoreflect.EnumType {
	return &file_pb_transcript_transcript_proto_enumTypes[2]
}

func (x DegreeStatus) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use DegreeStatus.Descriptor instead.
func (DegreeStatus) EnumDescriptor() ([]byte, []int) {
	return file_pb_transcript_transcript_proto_rawDescGZIP(), []int{2}
}

type GradeScale int32

const (
	GradeScale_LETTER    GradeScale = 0
	GradeScale_NUMERIC   GradeScale = 1
	GradeScale_GPA_4_0   GradeScale = 2
	GradeScale_PASS_FAIL GradeScale = 3
)

// Enum value maps for GradeScale.
var (
	GradeScale_name = map[int32]string{
		0: "LETTER",
		1: "NUMERIC",
		2: "GPA_4_0",
		3: "PASS_FAIL",
	}
	GradeScale_value = map[string]int32{
		"LETTER":    0,
		"NUMERIC":   1,
		"GPA_4_0":   2,
		"PASS_FAIL": 3,
	}
)

func (x GradeScale) Enum() *GradeScale {
	p := new(GradeScale)
	*p = x
	return p
}

func (x GradeScale) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (GradeScale) Descriptor() protoreflect.EnumDescriptor {
	return file_pb_transcript_transcript_proto_enumTypes[3].Descriptor()
}

func (GradeScale) Type() protoreflect.EnumType {
	return &file_pb_transcript_transcript_proto_enumTypes[3]
}

func (x GradeScale) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use GradeScale.Descriptor instead.
func (GradeScale) EnumDescriptor() ([]byte, []int) {
	return file_pb_transcript_transcript_proto_rawDescGZIP(), []int{3}
}

// Messages
type Degree struct {
	state           protoimpl.MessageState `protogen:"open.v1"`
	Id              int64                  `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	Name            string                 `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
	Level           DegreeLevel            `protobuf:"varint,3,opt,name=level,proto3,enum=transcript.DegreeLevel" json:"level,omitempty"`
	Description     string                 `protobuf:"bytes,4,opt,name=description,proto3" json:"description,omitempty"`
	RequiredCredits int32                  `protobuf:"varint,5,opt,name=required_credits,json=requiredCredits,proto3" json:"required_credits,omitempty"`
	MinGpa          float64                `protobuf:"fixed64,6,opt,name=min_gpa,json=minGpa,proto3" json:"min_gpa,omitempty"`
	CreatedAt       *timestamppb.Timestamp `protobuf:"bytes,7,opt,name=created_at,json=createdAt,proto3" json:"created_at,omitempty"`
	UpdatedAt       *timestamppb.Timestamp `protobuf:"bytes,8,opt,name=updated_at,json=updatedAt,proto3" json:"updated_at,omitempty"`
	unknownFields   protoimpl.UnknownFields
	sizeCache       protoimpl.SizeCache
}

func (x *Degree) Reset() {
	*x = Degree{}
	mi := &file_pb_transcript_transcript_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *Degree) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Degree) ProtoMessage() {}

func (x *Degree) ProtoReflect() protoreflect.Message {
	mi := &file_pb_transcript_transcript_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Degree.ProtoReflect.Descriptor instead.
func (*Degree) Descriptor() ([]byte, []int) {
	return file_pb_transcript_transcript_proto_rawDescGZIP(), []int{0}
}

func (x *Degree) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *Degree) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *Degree) GetLevel() DegreeLevel {
	if x != nil {
		return x.Level
	}
	return DegreeLevel_BACHELOR
}

func (x *Degree) GetDescription() string {
	if x != nil {
		return x.Description
	}
	return ""
}

func (x *Degree) GetRequiredCredits() int32 {
	if x != nil {
		return x.RequiredCredits
	}
	return 0
}

func (x *Degree) GetMinGpa() float64 {
	if x != nil {
		return x.MinGpa
	}
	return 0
}

func (x *Degree) GetCreatedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.CreatedAt
	}
	return nil
}

func (x *Degree) GetUpdatedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.UpdatedAt
	}
	return nil
}

type AcademicTranscript struct {
	state                 protoimpl.MessageState `protogen:"open.v1"`
	Id                    int64                  `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	UserId                int64                  `protobuf:"varint,2,opt,name=user_id,json=userId,proto3" json:"user_id,omitempty"`
	DegreeId              int64                  `protobuf:"varint,3,opt,name=degree_id,json=degreeId,proto3" json:"degree_id,omitempty"`
	CumulativeGpa         float64                `protobuf:"fixed64,4,opt,name=cumulative_gpa,json=cumulativeGpa,proto3" json:"cumulative_gpa,omitempty"`
	TotalCreditsAttempted int32                  `protobuf:"varint,5,opt,name=total_credits_attempted,json=totalCreditsAttempted,proto3" json:"total_credits_attempted,omitempty"`
	TotalCreditsEarned    int32                  `protobuf:"varint,6,opt,name=total_credits_earned,json=totalCreditsEarned,proto3" json:"total_credits_earned,omitempty"`
	AcademicStanding      AcademicStanding       `protobuf:"varint,7,opt,name=academic_standing,json=academicStanding,proto3,enum=transcript.AcademicStanding" json:"academic_standing,omitempty"`
	GraduationDate        *timestamppb.Timestamp `protobuf:"bytes,8,opt,name=graduation_date,json=graduationDate,proto3" json:"graduation_date,omitempty"`
	CreatedAt             *timestamppb.Timestamp `protobuf:"bytes,9,opt,name=created_at,json=createdAt,proto3" json:"created_at,omitempty"`
	UpdatedAt             *timestamppb.Timestamp `protobuf:"bytes,10,opt,name=updated_at,json=updatedAt,proto3" json:"updated_at,omitempty"`
	Degree                *Degree                `protobuf:"bytes,11,opt,name=degree,proto3" json:"degree,omitempty"` // populated when needed
	unknownFields         protoimpl.UnknownFields
	sizeCache             protoimpl.SizeCache
}

func (x *AcademicTranscript) Reset() {
	*x = AcademicTranscript{}
	mi := &file_pb_transcript_transcript_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *AcademicTranscript) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AcademicTranscript) ProtoMessage() {}

func (x *AcademicTranscript) ProtoReflect() protoreflect.Message {
	mi := &file_pb_transcript_transcript_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AcademicTranscript.ProtoReflect.Descriptor instead.
func (*AcademicTranscript) Descriptor() ([]byte, []int) {
	return file_pb_transcript_transcript_proto_rawDescGZIP(), []int{1}
}

func (x *AcademicTranscript) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *AcademicTranscript) GetUserId() int64 {
	if x != nil {
		return x.UserId
	}
	return 0
}

func (x *AcademicTranscript) GetDegreeId() int64 {
	if x != nil {
		return x.DegreeId
	}
	return 0
}

func (x *AcademicTranscript) GetCumulativeGpa() float64 {
	if x != nil {
		return x.CumulativeGpa
	}
	return 0
}

func (x *AcademicTranscript) GetTotalCreditsAttempted() int32 {
	if x != nil {
		return x.TotalCreditsAttempted
	}
	return 0
}

func (x *AcademicTranscript) GetTotalCreditsEarned() int32 {
	if x != nil {
		return x.TotalCreditsEarned
	}
	return 0
}

func (x *AcademicTranscript) GetAcademicStanding() AcademicStanding {
	if x != nil {
		return x.AcademicStanding
	}
	return AcademicStanding_GOOD_STANDING
}

func (x *AcademicTranscript) GetGraduationDate() *timestamppb.Timestamp {
	if x != nil {
		return x.GraduationDate
	}
	return nil
}

func (x *AcademicTranscript) GetCreatedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.CreatedAt
	}
	return nil
}

func (x *AcademicTranscript) GetUpdatedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.UpdatedAt
	}
	return nil
}

func (x *AcademicTranscript) GetDegree() *Degree {
	if x != nil {
		return x.Degree
	}
	return nil
}

type TranscriptEntry struct {
	state          protoimpl.MessageState `protogen:"open.v1"`
	Id             int64                  `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	TranscriptId   int64                  `protobuf:"varint,2,opt,name=transcript_id,json=transcriptId,proto3" json:"transcript_id,omitempty"`
	CourseId       int64                  `protobuf:"varint,3,opt,name=course_id,json=courseId,proto3" json:"course_id,omitempty"`
	ThreadId       int64                  `protobuf:"varint,4,opt,name=thread_id,json=threadId,proto3" json:"thread_id,omitempty"`
	SemesterId     int64                  `protobuf:"varint,5,opt,name=semester_id,json=semesterId,proto3" json:"semester_id,omitempty"`
	GradeLetter    string                 `protobuf:"bytes,6,opt,name=grade_letter,json=gradeLetter,proto3" json:"grade_letter,omitempty"`
	GradeNumeric   float64                `protobuf:"fixed64,7,opt,name=grade_numeric,json=gradeNumeric,proto3" json:"grade_numeric,omitempty"`
	GradePoints    float64                `protobuf:"fixed64,8,opt,name=grade_points,json=gradePoints,proto3" json:"grade_points,omitempty"`
	Credits        int32                  `protobuf:"varint,9,opt,name=credits,proto3" json:"credits,omitempty"`
	IsTransfer     bool                   `protobuf:"varint,10,opt,name=is_transfer,json=isTransfer,proto3" json:"is_transfer,omitempty"`
	IsRepeated     bool                   `protobuf:"varint,11,opt,name=is_repeated,json=isRepeated,proto3" json:"is_repeated,omitempty"`
	CompletionDate *timestamppb.Timestamp `protobuf:"bytes,12,opt,name=completion_date,json=completionDate,proto3" json:"completion_date,omitempty"`
	CreatedAt      *timestamppb.Timestamp `protobuf:"bytes,13,opt,name=created_at,json=createdAt,proto3" json:"created_at,omitempty"`
	UpdatedAt      *timestamppb.Timestamp `protobuf:"bytes,14,opt,name=updated_at,json=updatedAt,proto3" json:"updated_at,omitempty"`
	// Additional fields for display
	CourseTitle   string `protobuf:"bytes,15,opt,name=course_title,json=courseTitle,proto3" json:"course_title,omitempty"`
	SemesterName  string `protobuf:"bytes,16,opt,name=semester_name,json=semesterName,proto3" json:"semester_name,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *TranscriptEntry) Reset() {
	*x = TranscriptEntry{}
	mi := &file_pb_transcript_transcript_proto_msgTypes[2]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *TranscriptEntry) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TranscriptEntry) ProtoMessage() {}

func (x *TranscriptEntry) ProtoReflect() protoreflect.Message {
	mi := &file_pb_transcript_transcript_proto_msgTypes[2]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TranscriptEntry.ProtoReflect.Descriptor instead.
func (*TranscriptEntry) Descriptor() ([]byte, []int) {
	return file_pb_transcript_transcript_proto_rawDescGZIP(), []int{2}
}

func (x *TranscriptEntry) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *TranscriptEntry) GetTranscriptId() int64 {
	if x != nil {
		return x.TranscriptId
	}
	return 0
}

func (x *TranscriptEntry) GetCourseId() int64 {
	if x != nil {
		return x.CourseId
	}
	return 0
}

func (x *TranscriptEntry) GetThreadId() int64 {
	if x != nil {
		return x.ThreadId
	}
	return 0
}

func (x *TranscriptEntry) GetSemesterId() int64 {
	if x != nil {
		return x.SemesterId
	}
	return 0
}

func (x *TranscriptEntry) GetGradeLetter() string {
	if x != nil {
		return x.GradeLetter
	}
	return ""
}

func (x *TranscriptEntry) GetGradeNumeric() float64 {
	if x != nil {
		return x.GradeNumeric
	}
	return 0
}

func (x *TranscriptEntry) GetGradePoints() float64 {
	if x != nil {
		return x.GradePoints
	}
	return 0
}

func (x *TranscriptEntry) GetCredits() int32 {
	if x != nil {
		return x.Credits
	}
	return 0
}

func (x *TranscriptEntry) GetIsTransfer() bool {
	if x != nil {
		return x.IsTransfer
	}
	return false
}

func (x *TranscriptEntry) GetIsRepeated() bool {
	if x != nil {
		return x.IsRepeated
	}
	return false
}

func (x *TranscriptEntry) GetCompletionDate() *timestamppb.Timestamp {
	if x != nil {
		return x.CompletionDate
	}
	return nil
}

func (x *TranscriptEntry) GetCreatedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.CreatedAt
	}
	return nil
}

func (x *TranscriptEntry) GetUpdatedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.UpdatedAt
	}
	return nil
}

func (x *TranscriptEntry) GetCourseTitle() string {
	if x != nil {
		return x.CourseTitle
	}
	return ""
}

func (x *TranscriptEntry) GetSemesterName() string {
	if x != nil {
		return x.SemesterName
	}
	return ""
}

type StudentDegree struct {
	state                  protoimpl.MessageState `protogen:"open.v1"`
	Id                     int64                  `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	UserId                 int64                  `protobuf:"varint,2,opt,name=user_id,json=userId,proto3" json:"user_id,omitempty"`
	DegreeId               int64                  `protobuf:"varint,3,opt,name=degree_id,json=degreeId,proto3" json:"degree_id,omitempty"`
	Status                 DegreeStatus           `protobuf:"varint,4,opt,name=status,proto3,enum=transcript.DegreeStatus" json:"status,omitempty"`
	StartDate              *timestamppb.Timestamp `protobuf:"bytes,5,opt,name=start_date,json=startDate,proto3" json:"start_date,omitempty"`
	ExpectedGraduationDate *timestamppb.Timestamp `protobuf:"bytes,6,opt,name=expected_graduation_date,json=expectedGraduationDate,proto3" json:"expected_graduation_date,omitempty"`
	ActualGraduationDate   *timestamppb.Timestamp `protobuf:"bytes,7,opt,name=actual_graduation_date,json=actualGraduationDate,proto3" json:"actual_graduation_date,omitempty"`
	FinalGpa               float64                `protobuf:"fixed64,8,opt,name=final_gpa,json=finalGpa,proto3" json:"final_gpa,omitempty"`
	CreatedAt              *timestamppb.Timestamp `protobuf:"bytes,9,opt,name=created_at,json=createdAt,proto3" json:"created_at,omitempty"`
	UpdatedAt              *timestamppb.Timestamp `protobuf:"bytes,10,opt,name=updated_at,json=updatedAt,proto3" json:"updated_at,omitempty"`
	Degree                 *Degree                `protobuf:"bytes,11,opt,name=degree,proto3" json:"degree,omitempty"` // populated when needed
	unknownFields          protoimpl.UnknownFields
	sizeCache              protoimpl.SizeCache
}

func (x *StudentDegree) Reset() {
	*x = StudentDegree{}
	mi := &file_pb_transcript_transcript_proto_msgTypes[3]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *StudentDegree) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*StudentDegree) ProtoMessage() {}

func (x *StudentDegree) ProtoReflect() protoreflect.Message {
	mi := &file_pb_transcript_transcript_proto_msgTypes[3]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use StudentDegree.ProtoReflect.Descriptor instead.
func (*StudentDegree) Descriptor() ([]byte, []int) {
	return file_pb_transcript_transcript_proto_rawDescGZIP(), []int{3}
}

func (x *StudentDegree) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *StudentDegree) GetUserId() int64 {
	if x != nil {
		return x.UserId
	}
	return 0
}

func (x *StudentDegree) GetDegreeId() int64 {
	if x != nil {
		return x.DegreeId
	}
	return 0
}

func (x *StudentDegree) GetStatus() DegreeStatus {
	if x != nil {
		return x.Status
	}
	return DegreeStatus_IN_PROGRESS
}

func (x *StudentDegree) GetStartDate() *timestamppb.Timestamp {
	if x != nil {
		return x.StartDate
	}
	return nil
}

func (x *StudentDegree) GetExpectedGraduationDate() *timestamppb.Timestamp {
	if x != nil {
		return x.ExpectedGraduationDate
	}
	return nil
}

func (x *StudentDegree) GetActualGraduationDate() *timestamppb.Timestamp {
	if x != nil {
		return x.ActualGraduationDate
	}
	return nil
}

func (x *StudentDegree) GetFinalGpa() float64 {
	if x != nil {
		return x.FinalGpa
	}
	return 0
}

func (x *StudentDegree) GetCreatedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.CreatedAt
	}
	return nil
}

func (x *StudentDegree) GetUpdatedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.UpdatedAt
	}
	return nil
}

func (x *StudentDegree) GetDegree() *Degree {
	if x != nil {
		return x.Degree
	}
	return nil
}

// Request/Response messages
type CreateDegreeRequest struct {
	state           protoimpl.MessageState `protogen:"open.v1"`
	Name            string                 `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`
	Level           DegreeLevel            `protobuf:"varint,2,opt,name=level,proto3,enum=transcript.DegreeLevel" json:"level,omitempty"`
	Description     string                 `protobuf:"bytes,3,opt,name=description,proto3" json:"description,omitempty"`
	RequiredCredits int32                  `protobuf:"varint,4,opt,name=required_credits,json=requiredCredits,proto3" json:"required_credits,omitempty"`
	MinGpa          float64                `protobuf:"fixed64,5,opt,name=min_gpa,json=minGpa,proto3" json:"min_gpa,omitempty"`
	unknownFields   protoimpl.UnknownFields
	sizeCache       protoimpl.SizeCache
}

func (x *CreateDegreeRequest) Reset() {
	*x = CreateDegreeRequest{}
	mi := &file_pb_transcript_transcript_proto_msgTypes[4]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CreateDegreeRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateDegreeRequest) ProtoMessage() {}

func (x *CreateDegreeRequest) ProtoReflect() protoreflect.Message {
	mi := &file_pb_transcript_transcript_proto_msgTypes[4]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateDegreeRequest.ProtoReflect.Descriptor instead.
func (*CreateDegreeRequest) Descriptor() ([]byte, []int) {
	return file_pb_transcript_transcript_proto_rawDescGZIP(), []int{4}
}

func (x *CreateDegreeRequest) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *CreateDegreeRequest) GetLevel() DegreeLevel {
	if x != nil {
		return x.Level
	}
	return DegreeLevel_BACHELOR
}

func (x *CreateDegreeRequest) GetDescription() string {
	if x != nil {
		return x.Description
	}
	return ""
}

func (x *CreateDegreeRequest) GetRequiredCredits() int32 {
	if x != nil {
		return x.RequiredCredits
	}
	return 0
}

func (x *CreateDegreeRequest) GetMinGpa() float64 {
	if x != nil {
		return x.MinGpa
	}
	return 0
}

type DegreeResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Degree        *Degree                `protobuf:"bytes,1,opt,name=degree,proto3" json:"degree,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *DegreeResponse) Reset() {
	*x = DegreeResponse{}
	mi := &file_pb_transcript_transcript_proto_msgTypes[5]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *DegreeResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DegreeResponse) ProtoMessage() {}

func (x *DegreeResponse) ProtoReflect() protoreflect.Message {
	mi := &file_pb_transcript_transcript_proto_msgTypes[5]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DegreeResponse.ProtoReflect.Descriptor instead.
func (*DegreeResponse) Descriptor() ([]byte, []int) {
	return file_pb_transcript_transcript_proto_rawDescGZIP(), []int{5}
}

func (x *DegreeResponse) GetDegree() *Degree {
	if x != nil {
		return x.Degree
	}
	return nil
}

type GetDegreeRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Id            int64                  `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetDegreeRequest) Reset() {
	*x = GetDegreeRequest{}
	mi := &file_pb_transcript_transcript_proto_msgTypes[6]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetDegreeRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetDegreeRequest) ProtoMessage() {}

func (x *GetDegreeRequest) ProtoReflect() protoreflect.Message {
	mi := &file_pb_transcript_transcript_proto_msgTypes[6]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetDegreeRequest.ProtoReflect.Descriptor instead.
func (*GetDegreeRequest) Descriptor() ([]byte, []int) {
	return file_pb_transcript_transcript_proto_rawDescGZIP(), []int{6}
}

func (x *GetDegreeRequest) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

type ListDegreesRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Page          int32                  `protobuf:"varint,1,opt,name=page,proto3" json:"page,omitempty"`
	PageSize      int32                  `protobuf:"varint,2,opt,name=page_size,json=pageSize,proto3" json:"page_size,omitempty"`
	Level         DegreeLevel            `protobuf:"varint,3,opt,name=level,proto3,enum=transcript.DegreeLevel" json:"level,omitempty"` // optional filter
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ListDegreesRequest) Reset() {
	*x = ListDegreesRequest{}
	mi := &file_pb_transcript_transcript_proto_msgTypes[7]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ListDegreesRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListDegreesRequest) ProtoMessage() {}

func (x *ListDegreesRequest) ProtoReflect() protoreflect.Message {
	mi := &file_pb_transcript_transcript_proto_msgTypes[7]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListDegreesRequest.ProtoReflect.Descriptor instead.
func (*ListDegreesRequest) Descriptor() ([]byte, []int) {
	return file_pb_transcript_transcript_proto_rawDescGZIP(), []int{7}
}

func (x *ListDegreesRequest) GetPage() int32 {
	if x != nil {
		return x.Page
	}
	return 0
}

func (x *ListDegreesRequest) GetPageSize() int32 {
	if x != nil {
		return x.PageSize
	}
	return 0
}

func (x *ListDegreesRequest) GetLevel() DegreeLevel {
	if x != nil {
		return x.Level
	}
	return DegreeLevel_BACHELOR
}

type ListDegreesResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Degrees       []*Degree              `protobuf:"bytes,1,rep,name=degrees,proto3" json:"degrees,omitempty"`
	TotalCount    int32                  `protobuf:"varint,2,opt,name=total_count,json=totalCount,proto3" json:"total_count,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ListDegreesResponse) Reset() {
	*x = ListDegreesResponse{}
	mi := &file_pb_transcript_transcript_proto_msgTypes[8]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ListDegreesResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListDegreesResponse) ProtoMessage() {}

func (x *ListDegreesResponse) ProtoReflect() protoreflect.Message {
	mi := &file_pb_transcript_transcript_proto_msgTypes[8]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListDegreesResponse.ProtoReflect.Descriptor instead.
func (*ListDegreesResponse) Descriptor() ([]byte, []int) {
	return file_pb_transcript_transcript_proto_rawDescGZIP(), []int{8}
}

func (x *ListDegreesResponse) GetDegrees() []*Degree {
	if x != nil {
		return x.Degrees
	}
	return nil
}

func (x *ListDegreesResponse) GetTotalCount() int32 {
	if x != nil {
		return x.TotalCount
	}
	return 0
}

type CreateTranscriptRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	UserId        int64                  `protobuf:"varint,1,opt,name=user_id,json=userId,proto3" json:"user_id,omitempty"`
	DegreeId      int64                  `protobuf:"varint,2,opt,name=degree_id,json=degreeId,proto3" json:"degree_id,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *CreateTranscriptRequest) Reset() {
	*x = CreateTranscriptRequest{}
	mi := &file_pb_transcript_transcript_proto_msgTypes[9]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CreateTranscriptRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateTranscriptRequest) ProtoMessage() {}

func (x *CreateTranscriptRequest) ProtoReflect() protoreflect.Message {
	mi := &file_pb_transcript_transcript_proto_msgTypes[9]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateTranscriptRequest.ProtoReflect.Descriptor instead.
func (*CreateTranscriptRequest) Descriptor() ([]byte, []int) {
	return file_pb_transcript_transcript_proto_rawDescGZIP(), []int{9}
}

func (x *CreateTranscriptRequest) GetUserId() int64 {
	if x != nil {
		return x.UserId
	}
	return 0
}

func (x *CreateTranscriptRequest) GetDegreeId() int64 {
	if x != nil {
		return x.DegreeId
	}
	return 0
}

type TranscriptResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Transcript    *AcademicTranscript    `protobuf:"bytes,1,opt,name=transcript,proto3" json:"transcript,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *TranscriptResponse) Reset() {
	*x = TranscriptResponse{}
	mi := &file_pb_transcript_transcript_proto_msgTypes[10]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *TranscriptResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TranscriptResponse) ProtoMessage() {}

func (x *TranscriptResponse) ProtoReflect() protoreflect.Message {
	mi := &file_pb_transcript_transcript_proto_msgTypes[10]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TranscriptResponse.ProtoReflect.Descriptor instead.
func (*TranscriptResponse) Descriptor() ([]byte, []int) {
	return file_pb_transcript_transcript_proto_rawDescGZIP(), []int{10}
}

func (x *TranscriptResponse) GetTranscript() *AcademicTranscript {
	if x != nil {
		return x.Transcript
	}
	return nil
}

type GetTranscriptRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	UserId        int64                  `protobuf:"varint,1,opt,name=user_id,json=userId,proto3" json:"user_id,omitempty"`
	DegreeId      int64                  `protobuf:"varint,2,opt,name=degree_id,json=degreeId,proto3" json:"degree_id,omitempty"` // optional, if not provided returns primary transcript
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetTranscriptRequest) Reset() {
	*x = GetTranscriptRequest{}
	mi := &file_pb_transcript_transcript_proto_msgTypes[11]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetTranscriptRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetTranscriptRequest) ProtoMessage() {}

func (x *GetTranscriptRequest) ProtoReflect() protoreflect.Message {
	mi := &file_pb_transcript_transcript_proto_msgTypes[11]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetTranscriptRequest.ProtoReflect.Descriptor instead.
func (*GetTranscriptRequest) Descriptor() ([]byte, []int) {
	return file_pb_transcript_transcript_proto_rawDescGZIP(), []int{11}
}

func (x *GetTranscriptRequest) GetUserId() int64 {
	if x != nil {
		return x.UserId
	}
	return 0
}

func (x *GetTranscriptRequest) GetDegreeId() int64 {
	if x != nil {
		return x.DegreeId
	}
	return 0
}

type AddTranscriptEntryRequest struct {
	state          protoimpl.MessageState `protogen:"open.v1"`
	TranscriptId   int64                  `protobuf:"varint,1,opt,name=transcript_id,json=transcriptId,proto3" json:"transcript_id,omitempty"`
	CourseId       int64                  `protobuf:"varint,2,opt,name=course_id,json=courseId,proto3" json:"course_id,omitempty"`
	ThreadId       int64                  `protobuf:"varint,3,opt,name=thread_id,json=threadId,proto3" json:"thread_id,omitempty"`
	SemesterId     int64                  `protobuf:"varint,4,opt,name=semester_id,json=semesterId,proto3" json:"semester_id,omitempty"`
	GradeLetter    string                 `protobuf:"bytes,5,opt,name=grade_letter,json=gradeLetter,proto3" json:"grade_letter,omitempty"`
	GradeNumeric   float64                `protobuf:"fixed64,6,opt,name=grade_numeric,json=gradeNumeric,proto3" json:"grade_numeric,omitempty"`
	GradePoints    float64                `protobuf:"fixed64,7,opt,name=grade_points,json=gradePoints,proto3" json:"grade_points,omitempty"`
	Credits        int32                  `protobuf:"varint,8,opt,name=credits,proto3" json:"credits,omitempty"`
	IsTransfer     bool                   `protobuf:"varint,9,opt,name=is_transfer,json=isTransfer,proto3" json:"is_transfer,omitempty"`
	IsRepeated     bool                   `protobuf:"varint,10,opt,name=is_repeated,json=isRepeated,proto3" json:"is_repeated,omitempty"`
	CompletionDate *timestamppb.Timestamp `protobuf:"bytes,11,opt,name=completion_date,json=completionDate,proto3" json:"completion_date,omitempty"`
	unknownFields  protoimpl.UnknownFields
	sizeCache      protoimpl.SizeCache
}

func (x *AddTranscriptEntryRequest) Reset() {
	*x = AddTranscriptEntryRequest{}
	mi := &file_pb_transcript_transcript_proto_msgTypes[12]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *AddTranscriptEntryRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AddTranscriptEntryRequest) ProtoMessage() {}

func (x *AddTranscriptEntryRequest) ProtoReflect() protoreflect.Message {
	mi := &file_pb_transcript_transcript_proto_msgTypes[12]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AddTranscriptEntryRequest.ProtoReflect.Descriptor instead.
func (*AddTranscriptEntryRequest) Descriptor() ([]byte, []int) {
	return file_pb_transcript_transcript_proto_rawDescGZIP(), []int{12}
}

func (x *AddTranscriptEntryRequest) GetTranscriptId() int64 {
	if x != nil {
		return x.TranscriptId
	}
	return 0
}

func (x *AddTranscriptEntryRequest) GetCourseId() int64 {
	if x != nil {
		return x.CourseId
	}
	return 0
}

func (x *AddTranscriptEntryRequest) GetThreadId() int64 {
	if x != nil {
		return x.ThreadId
	}
	return 0
}

func (x *AddTranscriptEntryRequest) GetSemesterId() int64 {
	if x != nil {
		return x.SemesterId
	}
	return 0
}

func (x *AddTranscriptEntryRequest) GetGradeLetter() string {
	if x != nil {
		return x.GradeLetter
	}
	return ""
}

func (x *AddTranscriptEntryRequest) GetGradeNumeric() float64 {
	if x != nil {
		return x.GradeNumeric
	}
	return 0
}

func (x *AddTranscriptEntryRequest) GetGradePoints() float64 {
	if x != nil {
		return x.GradePoints
	}
	return 0
}

func (x *AddTranscriptEntryRequest) GetCredits() int32 {
	if x != nil {
		return x.Credits
	}
	return 0
}

func (x *AddTranscriptEntryRequest) GetIsTransfer() bool {
	if x != nil {
		return x.IsTransfer
	}
	return false
}

func (x *AddTranscriptEntryRequest) GetIsRepeated() bool {
	if x != nil {
		return x.IsRepeated
	}
	return false
}

func (x *AddTranscriptEntryRequest) GetCompletionDate() *timestamppb.Timestamp {
	if x != nil {
		return x.CompletionDate
	}
	return nil
}

type TranscriptEntryResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Entry         *TranscriptEntry       `protobuf:"bytes,1,opt,name=entry,proto3" json:"entry,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *TranscriptEntryResponse) Reset() {
	*x = TranscriptEntryResponse{}
	mi := &file_pb_transcript_transcript_proto_msgTypes[13]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *TranscriptEntryResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TranscriptEntryResponse) ProtoMessage() {}

func (x *TranscriptEntryResponse) ProtoReflect() protoreflect.Message {
	mi := &file_pb_transcript_transcript_proto_msgTypes[13]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TranscriptEntryResponse.ProtoReflect.Descriptor instead.
func (*TranscriptEntryResponse) Descriptor() ([]byte, []int) {
	return file_pb_transcript_transcript_proto_rawDescGZIP(), []int{13}
}

func (x *TranscriptEntryResponse) GetEntry() *TranscriptEntry {
	if x != nil {
		return x.Entry
	}
	return nil
}

type GetTranscriptEntriesRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	TranscriptId  int64                  `protobuf:"varint,1,opt,name=transcript_id,json=transcriptId,proto3" json:"transcript_id,omitempty"`
	SemesterId    int64                  `protobuf:"varint,2,opt,name=semester_id,json=semesterId,proto3" json:"semester_id,omitempty"` // optional filter
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetTranscriptEntriesRequest) Reset() {
	*x = GetTranscriptEntriesRequest{}
	mi := &file_pb_transcript_transcript_proto_msgTypes[14]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetTranscriptEntriesRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetTranscriptEntriesRequest) ProtoMessage() {}

func (x *GetTranscriptEntriesRequest) ProtoReflect() protoreflect.Message {
	mi := &file_pb_transcript_transcript_proto_msgTypes[14]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetTranscriptEntriesRequest.ProtoReflect.Descriptor instead.
func (*GetTranscriptEntriesRequest) Descriptor() ([]byte, []int) {
	return file_pb_transcript_transcript_proto_rawDescGZIP(), []int{14}
}

func (x *GetTranscriptEntriesRequest) GetTranscriptId() int64 {
	if x != nil {
		return x.TranscriptId
	}
	return 0
}

func (x *GetTranscriptEntriesRequest) GetSemesterId() int64 {
	if x != nil {
		return x.SemesterId
	}
	return 0
}

type GetTranscriptEntriesResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Entries       []*TranscriptEntry     `protobuf:"bytes,1,rep,name=entries,proto3" json:"entries,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetTranscriptEntriesResponse) Reset() {
	*x = GetTranscriptEntriesResponse{}
	mi := &file_pb_transcript_transcript_proto_msgTypes[15]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetTranscriptEntriesResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetTranscriptEntriesResponse) ProtoMessage() {}

func (x *GetTranscriptEntriesResponse) ProtoReflect() protoreflect.Message {
	mi := &file_pb_transcript_transcript_proto_msgTypes[15]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetTranscriptEntriesResponse.ProtoReflect.Descriptor instead.
func (*GetTranscriptEntriesResponse) Descriptor() ([]byte, []int) {
	return file_pb_transcript_transcript_proto_rawDescGZIP(), []int{15}
}

func (x *GetTranscriptEntriesResponse) GetEntries() []*TranscriptEntry {
	if x != nil {
		return x.Entries
	}
	return nil
}

type UpdateGPARequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	TranscriptId  int64                  `protobuf:"varint,1,opt,name=transcript_id,json=transcriptId,proto3" json:"transcript_id,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *UpdateGPARequest) Reset() {
	*x = UpdateGPARequest{}
	mi := &file_pb_transcript_transcript_proto_msgTypes[16]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UpdateGPARequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateGPARequest) ProtoMessage() {}

func (x *UpdateGPARequest) ProtoReflect() protoreflect.Message {
	mi := &file_pb_transcript_transcript_proto_msgTypes[16]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateGPARequest.ProtoReflect.Descriptor instead.
func (*UpdateGPARequest) Descriptor() ([]byte, []int) {
	return file_pb_transcript_transcript_proto_rawDescGZIP(), []int{16}
}

func (x *UpdateGPARequest) GetTranscriptId() int64 {
	if x != nil {
		return x.TranscriptId
	}
	return 0
}

type CalculateGPARequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	UserId        int64                  `protobuf:"varint,1,opt,name=user_id,json=userId,proto3" json:"user_id,omitempty"`
	DegreeId      int64                  `protobuf:"varint,2,opt,name=degree_id,json=degreeId,proto3" json:"degree_id,omitempty"` // optional
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *CalculateGPARequest) Reset() {
	*x = CalculateGPARequest{}
	mi := &file_pb_transcript_transcript_proto_msgTypes[17]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CalculateGPARequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CalculateGPARequest) ProtoMessage() {}

func (x *CalculateGPARequest) ProtoReflect() protoreflect.Message {
	mi := &file_pb_transcript_transcript_proto_msgTypes[17]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CalculateGPARequest.ProtoReflect.Descriptor instead.
func (*CalculateGPARequest) Descriptor() ([]byte, []int) {
	return file_pb_transcript_transcript_proto_rawDescGZIP(), []int{17}
}

func (x *CalculateGPARequest) GetUserId() int64 {
	if x != nil {
		return x.UserId
	}
	return 0
}

func (x *CalculateGPARequest) GetDegreeId() int64 {
	if x != nil {
		return x.DegreeId
	}
	return 0
}

type GPAResponse struct {
	state                 protoimpl.MessageState `protogen:"open.v1"`
	Gpa                   float64                `protobuf:"fixed64,1,opt,name=gpa,proto3" json:"gpa,omitempty"`
	TotalCreditsAttempted int32                  `protobuf:"varint,2,opt,name=total_credits_attempted,json=totalCreditsAttempted,proto3" json:"total_credits_attempted,omitempty"`
	TotalCreditsEarned    int32                  `protobuf:"varint,3,opt,name=total_credits_earned,json=totalCreditsEarned,proto3" json:"total_credits_earned,omitempty"`
	unknownFields         protoimpl.UnknownFields
	sizeCache             protoimpl.SizeCache
}

func (x *GPAResponse) Reset() {
	*x = GPAResponse{}
	mi := &file_pb_transcript_transcript_proto_msgTypes[18]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GPAResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GPAResponse) ProtoMessage() {}

func (x *GPAResponse) ProtoReflect() protoreflect.Message {
	mi := &file_pb_transcript_transcript_proto_msgTypes[18]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GPAResponse.ProtoReflect.Descriptor instead.
func (*GPAResponse) Descriptor() ([]byte, []int) {
	return file_pb_transcript_transcript_proto_rawDescGZIP(), []int{18}
}

func (x *GPAResponse) GetGpa() float64 {
	if x != nil {
		return x.Gpa
	}
	return 0
}

func (x *GPAResponse) GetTotalCreditsAttempted() int32 {
	if x != nil {
		return x.TotalCreditsAttempted
	}
	return 0
}

func (x *GPAResponse) GetTotalCreditsEarned() int32 {
	if x != nil {
		return x.TotalCreditsEarned
	}
	return 0
}

type CreateStudentDegreeRequest struct {
	state                  protoimpl.MessageState `protogen:"open.v1"`
	UserId                 int64                  `protobuf:"varint,1,opt,name=user_id,json=userId,proto3" json:"user_id,omitempty"`
	DegreeId               int64                  `protobuf:"varint,2,opt,name=degree_id,json=degreeId,proto3" json:"degree_id,omitempty"`
	StartDate              *timestamppb.Timestamp `protobuf:"bytes,3,opt,name=start_date,json=startDate,proto3" json:"start_date,omitempty"`
	ExpectedGraduationDate *timestamppb.Timestamp `protobuf:"bytes,4,opt,name=expected_graduation_date,json=expectedGraduationDate,proto3" json:"expected_graduation_date,omitempty"`
	unknownFields          protoimpl.UnknownFields
	sizeCache              protoimpl.SizeCache
}

func (x *CreateStudentDegreeRequest) Reset() {
	*x = CreateStudentDegreeRequest{}
	mi := &file_pb_transcript_transcript_proto_msgTypes[19]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CreateStudentDegreeRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateStudentDegreeRequest) ProtoMessage() {}

func (x *CreateStudentDegreeRequest) ProtoReflect() protoreflect.Message {
	mi := &file_pb_transcript_transcript_proto_msgTypes[19]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateStudentDegreeRequest.ProtoReflect.Descriptor instead.
func (*CreateStudentDegreeRequest) Descriptor() ([]byte, []int) {
	return file_pb_transcript_transcript_proto_rawDescGZIP(), []int{19}
}

func (x *CreateStudentDegreeRequest) GetUserId() int64 {
	if x != nil {
		return x.UserId
	}
	return 0
}

func (x *CreateStudentDegreeRequest) GetDegreeId() int64 {
	if x != nil {
		return x.DegreeId
	}
	return 0
}

func (x *CreateStudentDegreeRequest) GetStartDate() *timestamppb.Timestamp {
	if x != nil {
		return x.StartDate
	}
	return nil
}

func (x *CreateStudentDegreeRequest) GetExpectedGraduationDate() *timestamppb.Timestamp {
	if x != nil {
		return x.ExpectedGraduationDate
	}
	return nil
}

type StudentDegreeResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	StudentDegree *StudentDegree         `protobuf:"bytes,1,opt,name=student_degree,json=studentDegree,proto3" json:"student_degree,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *StudentDegreeResponse) Reset() {
	*x = StudentDegreeResponse{}
	mi := &file_pb_transcript_transcript_proto_msgTypes[20]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *StudentDegreeResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*StudentDegreeResponse) ProtoMessage() {}

func (x *StudentDegreeResponse) ProtoReflect() protoreflect.Message {
	mi := &file_pb_transcript_transcript_proto_msgTypes[20]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use StudentDegreeResponse.ProtoReflect.Descriptor instead.
func (*StudentDegreeResponse) Descriptor() ([]byte, []int) {
	return file_pb_transcript_transcript_proto_rawDescGZIP(), []int{20}
}

func (x *StudentDegreeResponse) GetStudentDegree() *StudentDegree {
	if x != nil {
		return x.StudentDegree
	}
	return nil
}

type GetStudentDegreesRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	UserId        int64                  `protobuf:"varint,1,opt,name=user_id,json=userId,proto3" json:"user_id,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetStudentDegreesRequest) Reset() {
	*x = GetStudentDegreesRequest{}
	mi := &file_pb_transcript_transcript_proto_msgTypes[21]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetStudentDegreesRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetStudentDegreesRequest) ProtoMessage() {}

func (x *GetStudentDegreesRequest) ProtoReflect() protoreflect.Message {
	mi := &file_pb_transcript_transcript_proto_msgTypes[21]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetStudentDegreesRequest.ProtoReflect.Descriptor instead.
func (*GetStudentDegreesRequest) Descriptor() ([]byte, []int) {
	return file_pb_transcript_transcript_proto_rawDescGZIP(), []int{21}
}

func (x *GetStudentDegreesRequest) GetUserId() int64 {
	if x != nil {
		return x.UserId
	}
	return 0
}

type GetStudentDegreesResponse struct {
	state          protoimpl.MessageState `protogen:"open.v1"`
	StudentDegrees []*StudentDegree       `protobuf:"bytes,1,rep,name=student_degrees,json=studentDegrees,proto3" json:"student_degrees,omitempty"`
	unknownFields  protoimpl.UnknownFields
	sizeCache      protoimpl.SizeCache
}

func (x *GetStudentDegreesResponse) Reset() {
	*x = GetStudentDegreesResponse{}
	mi := &file_pb_transcript_transcript_proto_msgTypes[22]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetStudentDegreesResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetStudentDegreesResponse) ProtoMessage() {}

func (x *GetStudentDegreesResponse) ProtoReflect() protoreflect.Message {
	mi := &file_pb_transcript_transcript_proto_msgTypes[22]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetStudentDegreesResponse.ProtoReflect.Descriptor instead.
func (*GetStudentDegreesResponse) Descriptor() ([]byte, []int) {
	return file_pb_transcript_transcript_proto_rawDescGZIP(), []int{22}
}

func (x *GetStudentDegreesResponse) GetStudentDegrees() []*StudentDegree {
	if x != nil {
		return x.StudentDegrees
	}
	return nil
}

type UpdateDegreeStatusRequest struct {
	state                protoimpl.MessageState `protogen:"open.v1"`
	StudentDegreeId      int64                  `protobuf:"varint,1,opt,name=student_degree_id,json=studentDegreeId,proto3" json:"student_degree_id,omitempty"`
	Status               DegreeStatus           `protobuf:"varint,2,opt,name=status,proto3,enum=transcript.DegreeStatus" json:"status,omitempty"`
	ActualGraduationDate *timestamppb.Timestamp `protobuf:"bytes,3,opt,name=actual_graduation_date,json=actualGraduationDate,proto3" json:"actual_graduation_date,omitempty"` // required if status is COMPLETED
	FinalGpa             float64                `protobuf:"fixed64,4,opt,name=final_gpa,json=finalGpa,proto3" json:"final_gpa,omitempty"`                                     // required if status is COMPLETED
	unknownFields        protoimpl.UnknownFields
	sizeCache            protoimpl.SizeCache
}

func (x *UpdateDegreeStatusRequest) Reset() {
	*x = UpdateDegreeStatusRequest{}
	mi := &file_pb_transcript_transcript_proto_msgTypes[23]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UpdateDegreeStatusRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateDegreeStatusRequest) ProtoMessage() {}

func (x *UpdateDegreeStatusRequest) ProtoReflect() protoreflect.Message {
	mi := &file_pb_transcript_transcript_proto_msgTypes[23]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateDegreeStatusRequest.ProtoReflect.Descriptor instead.
func (*UpdateDegreeStatusRequest) Descriptor() ([]byte, []int) {
	return file_pb_transcript_transcript_proto_rawDescGZIP(), []int{23}
}

func (x *UpdateDegreeStatusRequest) GetStudentDegreeId() int64 {
	if x != nil {
		return x.StudentDegreeId
	}
	return 0
}

func (x *UpdateDegreeStatusRequest) GetStatus() DegreeStatus {
	if x != nil {
		return x.Status
	}
	return DegreeStatus_IN_PROGRESS
}

func (x *UpdateDegreeStatusRequest) GetActualGraduationDate() *timestamppb.Timestamp {
	if x != nil {
		return x.ActualGraduationDate
	}
	return nil
}

func (x *UpdateDegreeStatusRequest) GetFinalGpa() float64 {
	if x != nil {
		return x.FinalGpa
	}
	return 0
}

type GenerateTranscriptReportRequest struct {
	state                  protoimpl.MessageState `protogen:"open.v1"`
	UserId                 int64                  `protobuf:"varint,1,opt,name=user_id,json=userId,proto3" json:"user_id,omitempty"`
	DegreeId               int64                  `protobuf:"varint,2,opt,name=degree_id,json=degreeId,proto3" json:"degree_id,omitempty"` // optional
	IncludeTransferCredits bool                   `protobuf:"varint,3,opt,name=include_transfer_credits,json=includeTransferCredits,proto3" json:"include_transfer_credits,omitempty"`
	IncludeRepeatedCourses bool                   `protobuf:"varint,4,opt,name=include_repeated_courses,json=includeRepeatedCourses,proto3" json:"include_repeated_courses,omitempty"`
	unknownFields          protoimpl.UnknownFields
	sizeCache              protoimpl.SizeCache
}

func (x *GenerateTranscriptReportRequest) Reset() {
	*x = GenerateTranscriptReportRequest{}
	mi := &file_pb_transcript_transcript_proto_msgTypes[24]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GenerateTranscriptReportRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GenerateTranscriptReportRequest) ProtoMessage() {}

func (x *GenerateTranscriptReportRequest) ProtoReflect() protoreflect.Message {
	mi := &file_pb_transcript_transcript_proto_msgTypes[24]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GenerateTranscriptReportRequest.ProtoReflect.Descriptor instead.
func (*GenerateTranscriptReportRequest) Descriptor() ([]byte, []int) {
	return file_pb_transcript_transcript_proto_rawDescGZIP(), []int{24}
}

func (x *GenerateTranscriptReportRequest) GetUserId() int64 {
	if x != nil {
		return x.UserId
	}
	return 0
}

func (x *GenerateTranscriptReportRequest) GetDegreeId() int64 {
	if x != nil {
		return x.DegreeId
	}
	return 0
}

func (x *GenerateTranscriptReportRequest) GetIncludeTransferCredits() bool {
	if x != nil {
		return x.IncludeTransferCredits
	}
	return false
}

func (x *GenerateTranscriptReportRequest) GetIncludeRepeatedCourses() bool {
	if x != nil {
		return x.IncludeRepeatedCourses
	}
	return false
}

type TranscriptReport struct {
	state                       protoimpl.MessageState `protogen:"open.v1"`
	Transcript                  *AcademicTranscript    `protobuf:"bytes,1,opt,name=transcript,proto3" json:"transcript,omitempty"`
	Entries                     []*TranscriptEntry     `protobuf:"bytes,2,rep,name=entries,proto3" json:"entries,omitempty"`
	StudentDegree               *StudentDegree         `protobuf:"bytes,3,opt,name=student_degree,json=studentDegree,proto3" json:"student_degree,omitempty"`
	SemesterGpa                 float64                `protobuf:"fixed64,4,opt,name=semester_gpa,json=semesterGpa,proto3" json:"semester_gpa,omitempty"` // current semester GPA
	AcademicStandingDescription string                 `protobuf:"bytes,5,opt,name=academic_standing_description,json=academicStandingDescription,proto3" json:"academic_standing_description,omitempty"`
	unknownFields               protoimpl.UnknownFields
	sizeCache                   protoimpl.SizeCache
}

func (x *TranscriptReport) Reset() {
	*x = TranscriptReport{}
	mi := &file_pb_transcript_transcript_proto_msgTypes[25]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *TranscriptReport) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TranscriptReport) ProtoMessage() {}

func (x *TranscriptReport) ProtoReflect() protoreflect.Message {
	mi := &file_pb_transcript_transcript_proto_msgTypes[25]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TranscriptReport.ProtoReflect.Descriptor instead.
func (*TranscriptReport) Descriptor() ([]byte, []int) {
	return file_pb_transcript_transcript_proto_rawDescGZIP(), []int{25}
}

func (x *TranscriptReport) GetTranscript() *AcademicTranscript {
	if x != nil {
		return x.Transcript
	}
	return nil
}

func (x *TranscriptReport) GetEntries() []*TranscriptEntry {
	if x != nil {
		return x.Entries
	}
	return nil
}

func (x *TranscriptReport) GetStudentDegree() *StudentDegree {
	if x != nil {
		return x.StudentDegree
	}
	return nil
}

func (x *TranscriptReport) GetSemesterGpa() float64 {
	if x != nil {
		return x.SemesterGpa
	}
	return 0
}

func (x *TranscriptReport) GetAcademicStandingDescription() string {
	if x != nil {
		return x.AcademicStandingDescription
	}
	return ""
}

var File_pb_transcript_transcript_proto protoreflect.FileDescriptor

const file_pb_transcript_transcript_proto_rawDesc = "" +
	"\n" +
	"\x1epb/transcript/transcript.proto\x12\n" +
	"transcript\x1a\x1fgoogle/protobuf/timestamp.proto\"\xb7\x02\n" +
	"\x06Degree\x12\x0e\n" +
	"\x02id\x18\x01 \x01(\x03R\x02id\x12\x12\n" +
	"\x04name\x18\x02 \x01(\tR\x04name\x12-\n" +
	"\x05level\x18\x03 \x01(\x0e2\x17.transcript.DegreeLevelR\x05level\x12 \n" +
	"\vdescription\x18\x04 \x01(\tR\vdescription\x12)\n" +
	"\x10required_credits\x18\x05 \x01(\x05R\x0frequiredCredits\x12\x17\n" +
	"\amin_gpa\x18\x06 \x01(\x01R\x06minGpa\x129\n" +
	"\n" +
	"created_at\x18\a \x01(\v2\x1a.google.protobuf.TimestampR\tcreatedAt\x129\n" +
	"\n" +
	"updated_at\x18\b \x01(\v2\x1a.google.protobuf.TimestampR\tupdatedAt\"\x9d\x04\n" +
	"\x12AcademicTranscript\x12\x0e\n" +
	"\x02id\x18\x01 \x01(\x03R\x02id\x12\x17\n" +
	"\auser_id\x18\x02 \x01(\x03R\x06userId\x12\x1b\n" +
	"\tdegree_id\x18\x03 \x01(\x03R\bdegreeId\x12%\n" +
	"\x0ecumulative_gpa\x18\x04 \x01(\x01R\rcumulativeGpa\x126\n" +
	"\x17total_credits_attempted\x18\x05 \x01(\x05R\x15totalCreditsAttempted\x120\n" +
	"\x14total_credits_earned\x18\x06 \x01(\x05R\x12totalCreditsEarned\x12I\n" +
	"\x11academic_standing\x18\a \x01(\x0e2\x1c.transcript.AcademicStandingR\x10academicStanding\x12C\n" +
	"\x0fgraduation_date\x18\b \x01(\v2\x1a.google.protobuf.TimestampR\x0egraduationDate\x129\n" +
	"\n" +
	"created_at\x18\t \x01(\v2\x1a.google.protobuf.TimestampR\tcreatedAt\x129\n" +
	"\n" +
	"updated_at\x18\n" +
	" \x01(\v2\x1a.google.protobuf.TimestampR\tupdatedAt\x12*\n" +
	"\x06degree\x18\v \x01(\v2\x12.transcript.DegreeR\x06degree\"\xeb\x04\n" +
	"\x0fTranscriptEntry\x12\x0e\n" +
	"\x02id\x18\x01 \x01(\x03R\x02id\x12#\n" +
	"\rtranscript_id\x18\x02 \x01(\x03R\ftranscriptId\x12\x1b\n" +
	"\tcourse_id\x18\x03 \x01(\x03R\bcourseId\x12\x1b\n" +
	"\tthread_id\x18\x04 \x01(\x03R\bthreadId\x12\x1f\n" +
	"\vsemester_id\x18\x05 \x01(\x03R\n" +
	"semesterId\x12!\n" +
	"\fgrade_letter\x18\x06 \x01(\tR\vgradeLetter\x12#\n" +
	"\rgrade_numeric\x18\a \x01(\x01R\fgradeNumeric\x12!\n" +
	"\fgrade_points\x18\b \x01(\x01R\vgradePoints\x12\x18\n" +
	"\acredits\x18\t \x01(\x05R\acredits\x12\x1f\n" +
	"\vis_transfer\x18\n" +
	" \x01(\bR\n" +
	"isTransfer\x12\x1f\n" +
	"\vis_repeated\x18\v \x01(\bR\n" +
	"isRepeated\x12C\n" +
	"\x0fcompletion_date\x18\f \x01(\v2\x1a.google.protobuf.TimestampR\x0ecompletionDate\x129\n" +
	"\n" +
	"created_at\x18\r \x01(\v2\x1a.google.protobuf.TimestampR\tcreatedAt\x129\n" +
	"\n" +
	"updated_at\x18\x0e \x01(\v2\x1a.google.protobuf.TimestampR\tupdatedAt\x12!\n" +
	"\fcourse_title\x18\x0f \x01(\tR\vcourseTitle\x12#\n" +
	"\rsemester_name\x18\x10 \x01(\tR\fsemesterName\"\xa9\x04\n" +
	"\rStudentDegree\x12\x0e\n" +
	"\x02id\x18\x01 \x01(\x03R\x02id\x12\x17\n" +
	"\auser_id\x18\x02 \x01(\x03R\x06userId\x12\x1b\n" +
	"\tdegree_id\x18\x03 \x01(\x03R\bdegreeId\x120\n" +
	"\x06status\x18\x04 \x01(\x0e2\x18.transcript.DegreeStatusR\x06status\x129\n" +
	"\n" +
	"start_date\x18\x05 \x01(\v2\x1a.google.protobuf.TimestampR\tstartDate\x12T\n" +
	"\x18expected_graduation_date\x18\x06 \x01(\v2\x1a.google.protobuf.TimestampR\x16expectedGraduationDate\x12P\n" +
	"\x16actual_graduation_date\x18\a \x01(\v2\x1a.google.protobuf.TimestampR\x14actualGraduationDate\x12\x1b\n" +
	"\tfinal_gpa\x18\b \x01(\x01R\bfinalGpa\x129\n" +
	"\n" +
	"created_at\x18\t \x01(\v2\x1a.google.protobuf.TimestampR\tcreatedAt\x129\n" +
	"\n" +
	"updated_at\x18\n" +
	" \x01(\v2\x1a.google.protobuf.TimestampR\tupdatedAt\x12*\n" +
	"\x06degree\x18\v \x01(\v2\x12.transcript.DegreeR\x06degree\"\xbe\x01\n" +
	"\x13CreateDegreeRequest\x12\x12\n" +
	"\x04name\x18\x01 \x01(\tR\x04name\x12-\n" +
	"\x05level\x18\x02 \x01(\x0e2\x17.transcript.DegreeLevelR\x05level\x12 \n" +
	"\vdescription\x18\x03 \x01(\tR\vdescription\x12)\n" +
	"\x10required_credits\x18\x04 \x01(\x05R\x0frequiredCredits\x12\x17\n" +
	"\amin_gpa\x18\x05 \x01(\x01R\x06minGpa\"<\n" +
	"\x0eDegreeResponse\x12*\n" +
	"\x06degree\x18\x01 \x01(\v2\x12.transcript.DegreeR\x06degree\"\"\n" +
	"\x10GetDegreeRequest\x12\x0e\n" +
	"\x02id\x18\x01 \x01(\x03R\x02id\"t\n" +
	"\x12ListDegreesRequest\x12\x12\n" +
	"\x04page\x18\x01 \x01(\x05R\x04page\x12\x1b\n" +
	"\tpage_size\x18\x02 \x01(\x05R\bpageSize\x12-\n" +
	"\x05level\x18\x03 \x01(\x0e2\x17.transcript.DegreeLevelR\x05level\"d\n" +
	"\x13ListDegreesResponse\x12,\n" +
	"\adegrees\x18\x01 \x03(\v2\x12.transcript.DegreeR\adegrees\x12\x1f\n" +
	"\vtotal_count\x18\x02 \x01(\x05R\n" +
	"totalCount\"O\n" +
	"\x17CreateTranscriptRequest\x12\x17\n" +
	"\auser_id\x18\x01 \x01(\x03R\x06userId\x12\x1b\n" +
	"\tdegree_id\x18\x02 \x01(\x03R\bdegreeId\"T\n" +
	"\x12TranscriptResponse\x12>\n" +
	"\n" +
	"transcript\x18\x01 \x01(\v2\x1e.transcript.AcademicTranscriptR\n" +
	"transcript\"L\n" +
	"\x14GetTranscriptRequest\x12\x17\n" +
	"\auser_id\x18\x01 \x01(\x03R\x06userId\x12\x1b\n" +
	"\tdegree_id\x18\x02 \x01(\x03R\bdegreeId\"\xa7\x03\n" +
	"\x19AddTranscriptEntryRequest\x12#\n" +
	"\rtranscript_id\x18\x01 \x01(\x03R\ftranscriptId\x12\x1b\n" +
	"\tcourse_id\x18\x02 \x01(\x03R\bcourseId\x12\x1b\n" +
	"\tthread_id\x18\x03 \x01(\x03R\bthreadId\x12\x1f\n" +
	"\vsemester_id\x18\x04 \x01(\x03R\n" +
	"semesterId\x12!\n" +
	"\fgrade_letter\x18\x05 \x01(\tR\vgradeLetter\x12#\n" +
	"\rgrade_numeric\x18\x06 \x01(\x01R\fgradeNumeric\x12!\n" +
	"\fgrade_points\x18\a \x01(\x01R\vgradePoints\x12\x18\n" +
	"\acredits\x18\b \x01(\x05R\acredits\x12\x1f\n" +
	"\vis_transfer\x18\t \x01(\bR\n" +
	"isTransfer\x12\x1f\n" +
	"\vis_repeated\x18\n" +
	" \x01(\bR\n" +
	"isRepeated\x12C\n" +
	"\x0fcompletion_date\x18\v \x01(\v2\x1a.google.protobuf.TimestampR\x0ecompletionDate\"L\n" +
	"\x17TranscriptEntryResponse\x121\n" +
	"\x05entry\x18\x01 \x01(\v2\x1b.transcript.TranscriptEntryR\x05entry\"c\n" +
	"\x1bGetTranscriptEntriesRequest\x12#\n" +
	"\rtranscript_id\x18\x01 \x01(\x03R\ftranscriptId\x12\x1f\n" +
	"\vsemester_id\x18\x02 \x01(\x03R\n" +
	"semesterId\"U\n" +
	"\x1cGetTranscriptEntriesResponse\x125\n" +
	"\aentries\x18\x01 \x03(\v2\x1b.transcript.TranscriptEntryR\aentries\"7\n" +
	"\x10UpdateGPARequest\x12#\n" +
	"\rtranscript_id\x18\x01 \x01(\x03R\ftranscriptId\"K\n" +
	"\x13CalculateGPARequest\x12\x17\n" +
	"\auser_id\x18\x01 \x01(\x03R\x06userId\x12\x1b\n" +
	"\tdegree_id\x18\x02 \x01(\x03R\bdegreeId\"\x89\x01\n" +
	"\vGPAResponse\x12\x10\n" +
	"\x03gpa\x18\x01 \x01(\x01R\x03gpa\x126\n" +
	"\x17total_credits_attempted\x18\x02 \x01(\x05R\x15totalCreditsAttempted\x120\n" +
	"\x14total_credits_earned\x18\x03 \x01(\x05R\x12totalCreditsEarned\"\xe3\x01\n" +
	"\x1aCreateStudentDegreeRequest\x12\x17\n" +
	"\auser_id\x18\x01 \x01(\x03R\x06userId\x12\x1b\n" +
	"\tdegree_id\x18\x02 \x01(\x03R\bdegreeId\x129\n" +
	"\n" +
	"start_date\x18\x03 \x01(\v2\x1a.google.protobuf.TimestampR\tstartDate\x12T\n" +
	"\x18expected_graduation_date\x18\x04 \x01(\v2\x1a.google.protobuf.TimestampR\x16expectedGraduationDate\"Y\n" +
	"\x15StudentDegreeResponse\x12@\n" +
	"\x0estudent_degree\x18\x01 \x01(\v2\x19.transcript.StudentDegreeR\rstudentDegree\"3\n" +
	"\x18GetStudentDegreesRequest\x12\x17\n" +
	"\auser_id\x18\x01 \x01(\x03R\x06userId\"_\n" +
	"\x19GetStudentDegreesResponse\x12B\n" +
	"\x0fstudent_degrees\x18\x01 \x03(\v2\x19.transcript.StudentDegreeR\x0estudentDegrees\"\xe8\x01\n" +
	"\x19UpdateDegreeStatusRequest\x12*\n" +
	"\x11student_degree_id\x18\x01 \x01(\x03R\x0fstudentDegreeId\x120\n" +
	"\x06status\x18\x02 \x01(\x0e2\x18.transcript.DegreeStatusR\x06status\x12P\n" +
	"\x16actual_graduation_date\x18\x03 \x01(\v2\x1a.google.protobuf.TimestampR\x14actualGraduationDate\x12\x1b\n" +
	"\tfinal_gpa\x18\x04 \x01(\x01R\bfinalGpa\"\xcb\x01\n" +
	"\x1fGenerateTranscriptReportRequest\x12\x17\n" +
	"\auser_id\x18\x01 \x01(\x03R\x06userId\x12\x1b\n" +
	"\tdegree_id\x18\x02 \x01(\x03R\bdegreeId\x128\n" +
	"\x18include_transfer_credits\x18\x03 \x01(\bR\x16includeTransferCredits\x128\n" +
	"\x18include_repeated_courses\x18\x04 \x01(\bR\x16includeRepeatedCourses\"\xb2\x02\n" +
	"\x10TranscriptReport\x12>\n" +
	"\n" +
	"transcript\x18\x01 \x01(\v2\x1e.transcript.AcademicTranscriptR\n" +
	"transcript\x125\n" +
	"\aentries\x18\x02 \x03(\v2\x1b.transcript.TranscriptEntryR\aentries\x12@\n" +
	"\x0estudent_degree\x18\x03 \x01(\v2\x19.transcript.StudentDegreeR\rstudentDegree\x12!\n" +
	"\fsemester_gpa\x18\x04 \x01(\x01R\vsemesterGpa\x12B\n" +
	"\x1dacademic_standing_description\x18\x05 \x01(\tR\x1bacademicStandingDescription*{\n" +
	"\x10AcademicStanding\x12\x11\n" +
	"\rGOOD_STANDING\x10\x00\x12\x14\n" +
	"\x10ACADEMIC_WARNING\x10\x01\x12\x16\n" +
	"\x12ACADEMIC_PROBATION\x10\x02\x12\x17\n" +
	"\x13ACADEMIC_SUSPENSION\x10\x03\x12\r\n" +
	"\tDISMISSED\x10\x04*N\n" +
	"\vDegreeLevel\x12\f\n" +
	"\bBACHELOR\x10\x00\x12\n" +
	"\n" +
	"\x06MASTER\x10\x01\x12\a\n" +
	"\x03PHD\x10\x02\x12\x0f\n" +
	"\vCERTIFICATE\x10\x03\x12\v\n" +
	"\aDIPLOMA\x10\x04*N\n" +
	"\fDegreeStatus\x12\x0f\n" +
	"\vIN_PROGRESS\x10\x00\x12\r\n" +
	"\tCOMPLETED\x10\x01\x12\r\n" +
	"\tWITHDRAWN\x10\x02\x12\x0f\n" +
	"\vTRANSFERRED\x10\x03*A\n" +
	"\n" +
	"GradeScale\x12\n" +
	"\n" +
	"\x06LETTER\x10\x00\x12\v\n" +
	"\aNUMERIC\x10\x01\x12\v\n" +
	"\aGPA_4_0\x10\x02\x12\r\n" +
	"\tPASS_FAIL\x10\x032\x90\t\n" +
	"\x11TranscriptService\x12K\n" +
	"\fCreateDegree\x12\x1f.transcript.CreateDegreeRequest\x1a\x1a.transcript.DegreeResponse\x12E\n" +
	"\tGetDegree\x12\x1c.transcript.GetDegreeRequest\x1a\x1a.transcript.DegreeResponse\x12N\n" +
	"\vListDegrees\x12\x1e.transcript.ListDegreesRequest\x1a\x1f.transcript.ListDegreesResponse\x12W\n" +
	"\x10CreateTranscript\x12#.transcript.CreateTranscriptRequest\x1a\x1e.transcript.TranscriptResponse\x12Q\n" +
	"\rGetTranscript\x12 .transcript.GetTranscriptRequest\x1a\x1e.transcript.TranscriptResponse\x12`\n" +
	"\x12AddTranscriptEntry\x12%.transcript.AddTranscriptEntryRequest\x1a#.transcript.TranscriptEntryResponse\x12i\n" +
	"\x14GetTranscriptEntries\x12'.transcript.GetTranscriptEntriesRequest\x1a(.transcript.GetTranscriptEntriesResponse\x12I\n" +
	"\tUpdateGPA\x12\x1c.transcript.UpdateGPARequest\x1a\x1e.transcript.TranscriptResponse\x12H\n" +
	"\fCalculateGPA\x12\x1f.transcript.CalculateGPARequest\x1a\x17.transcript.GPAResponse\x12`\n" +
	"\x13CreateStudentDegree\x12&.transcript.CreateStudentDegreeRequest\x1a!.transcript.StudentDegreeResponse\x12`\n" +
	"\x11GetStudentDegrees\x12$.transcript.GetStudentDegreesRequest\x1a%.transcript.GetStudentDegreesResponse\x12^\n" +
	"\x12UpdateDegreeStatus\x12%.transcript.UpdateDegreeStatusRequest\x1a!.transcript.StudentDegreeResponse\x12e\n" +
	"\x18GenerateTranscriptReport\x12+.transcript.GenerateTranscriptReportRequest\x1a\x1c.transcript.TranscriptReportB1Z/github.com/edunite/course_service/pb/transcriptb\x06proto3"

var (
	file_pb_transcript_transcript_proto_rawDescOnce sync.Once
	file_pb_transcript_transcript_proto_rawDescData []byte
)

func file_pb_transcript_transcript_proto_rawDescGZIP() []byte {
	file_pb_transcript_transcript_proto_rawDescOnce.Do(func() {
		file_pb_transcript_transcript_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_pb_transcript_transcript_proto_rawDesc), len(file_pb_transcript_transcript_proto_rawDesc)))
	})
	return file_pb_transcript_transcript_proto_rawDescData
}

var file_pb_transcript_transcript_proto_enumTypes = make([]protoimpl.EnumInfo, 4)
var file_pb_transcript_transcript_proto_msgTypes = make([]protoimpl.MessageInfo, 26)
var file_pb_transcript_transcript_proto_goTypes = []any{
	(AcademicStanding)(0),                   // 0: transcript.AcademicStanding
	(DegreeLevel)(0),                        // 1: transcript.DegreeLevel
	(DegreeStatus)(0),                       // 2: transcript.DegreeStatus
	(GradeScale)(0),                         // 3: transcript.GradeScale
	(*Degree)(nil),                          // 4: transcript.Degree
	(*AcademicTranscript)(nil),              // 5: transcript.AcademicTranscript
	(*TranscriptEntry)(nil),                 // 6: transcript.TranscriptEntry
	(*StudentDegree)(nil),                   // 7: transcript.StudentDegree
	(*CreateDegreeRequest)(nil),             // 8: transcript.CreateDegreeRequest
	(*DegreeResponse)(nil),                  // 9: transcript.DegreeResponse
	(*GetDegreeRequest)(nil),                // 10: transcript.GetDegreeRequest
	(*ListDegreesRequest)(nil),              // 11: transcript.ListDegreesRequest
	(*ListDegreesResponse)(nil),             // 12: transcript.ListDegreesResponse
	(*CreateTranscriptRequest)(nil),         // 13: transcript.CreateTranscriptRequest
	(*TranscriptResponse)(nil),              // 14: transcript.TranscriptResponse
	(*GetTranscriptRequest)(nil),            // 15: transcript.GetTranscriptRequest
	(*AddTranscriptEntryRequest)(nil),       // 16: transcript.AddTranscriptEntryRequest
	(*TranscriptEntryResponse)(nil),         // 17: transcript.TranscriptEntryResponse
	(*GetTranscriptEntriesRequest)(nil),     // 18: transcript.GetTranscriptEntriesRequest
	(*GetTranscriptEntriesResponse)(nil),    // 19: transcript.GetTranscriptEntriesResponse
	(*UpdateGPARequest)(nil),                // 20: transcript.UpdateGPARequest
	(*CalculateGPARequest)(nil),             // 21: transcript.CalculateGPARequest
	(*GPAResponse)(nil),                     // 22: transcript.GPAResponse
	(*CreateStudentDegreeRequest)(nil),      // 23: transcript.CreateStudentDegreeRequest
	(*StudentDegreeResponse)(nil),           // 24: transcript.StudentDegreeResponse
	(*GetStudentDegreesRequest)(nil),        // 25: transcript.GetStudentDegreesRequest
	(*GetStudentDegreesResponse)(nil),       // 26: transcript.GetStudentDegreesResponse
	(*UpdateDegreeStatusRequest)(nil),       // 27: transcript.UpdateDegreeStatusRequest
	(*GenerateTranscriptReportRequest)(nil), // 28: transcript.GenerateTranscriptReportRequest
	(*TranscriptReport)(nil),                // 29: transcript.TranscriptReport
	(*timestamppb.Timestamp)(nil),           // 30: google.protobuf.Timestamp
}
var file_pb_transcript_transcript_proto_depIdxs = []int32{
	1,  // 0: transcript.Degree.level:type_name -> transcript.DegreeLevel
	30, // 1: transcript.Degree.created_at:type_name -> google.protobuf.Timestamp
	30, // 2: transcript.Degree.updated_at:type_name -> google.protobuf.Timestamp
	0,  // 3: transcript.AcademicTranscript.academic_standing:type_name -> transcript.AcademicStanding
	30, // 4: transcript.AcademicTranscript.graduation_date:type_name -> google.protobuf.Timestamp
	30, // 5: transcript.AcademicTranscript.created_at:type_name -> google.protobuf.Timestamp
	30, // 6: transcript.AcademicTranscript.updated_at:type_name -> google.protobuf.Timestamp
	4,  // 7: transcript.AcademicTranscript.degree:type_name -> transcript.Degree
	30, // 8: transcript.TranscriptEntry.completion_date:type_name -> google.protobuf.Timestamp
	30, // 9: transcript.TranscriptEntry.created_at:type_name -> google.protobuf.Timestamp
	30, // 10: transcript.TranscriptEntry.updated_at:type_name -> google.protobuf.Timestamp
	2,  // 11: transcript.StudentDegree.status:type_name -> transcript.DegreeStatus
	30, // 12: transcript.StudentDegree.start_date:type_name -> google.protobuf.Timestamp
	30, // 13: transcript.StudentDegree.expected_graduation_date:type_name -> google.protobuf.Timestamp
	30, // 14: transcript.StudentDegree.actual_graduation_date:type_name -> google.protobuf.Timestamp
	30, // 15: transcript.StudentDegree.created_at:type_name -> google.protobuf.Timestamp
	30, // 16: transcript.StudentDegree.updated_at:type_name -> google.protobuf.Timestamp
	4,  // 17: transcript.StudentDegree.degree:type_name -> transcript.Degree
	1,  // 18: transcript.CreateDegreeRequest.level:type_name -> transcript.DegreeLevel
	4,  // 19: transcript.DegreeResponse.degree:type_name -> transcript.Degree
	1,  // 20: transcript.ListDegreesRequest.level:type_name -> transcript.DegreeLevel
	4,  // 21: transcript.ListDegreesResponse.degrees:type_name -> transcript.Degree
	5,  // 22: transcript.TranscriptResponse.transcript:type_name -> transcript.AcademicTranscript
	30, // 23: transcript.AddTranscriptEntryRequest.completion_date:type_name -> google.protobuf.Timestamp
	6,  // 24: transcript.TranscriptEntryResponse.entry:type_name -> transcript.TranscriptEntry
	6,  // 25: transcript.GetTranscriptEntriesResponse.entries:type_name -> transcript.TranscriptEntry
	30, // 26: transcript.CreateStudentDegreeRequest.start_date:type_name -> google.protobuf.Timestamp
	30, // 27: transcript.CreateStudentDegreeRequest.expected_graduation_date:type_name -> google.protobuf.Timestamp
	7,  // 28: transcript.StudentDegreeResponse.student_degree:type_name -> transcript.StudentDegree
	7,  // 29: transcript.GetStudentDegreesResponse.student_degrees:type_name -> transcript.StudentDegree
	2,  // 30: transcript.UpdateDegreeStatusRequest.status:type_name -> transcript.DegreeStatus
	30, // 31: transcript.UpdateDegreeStatusRequest.actual_graduation_date:type_name -> google.protobuf.Timestamp
	5,  // 32: transcript.TranscriptReport.transcript:type_name -> transcript.AcademicTranscript
	6,  // 33: transcript.TranscriptReport.entries:type_name -> transcript.TranscriptEntry
	7,  // 34: transcript.TranscriptReport.student_degree:type_name -> transcript.StudentDegree
	8,  // 35: transcript.TranscriptService.CreateDegree:input_type -> transcript.CreateDegreeRequest
	10, // 36: transcript.TranscriptService.GetDegree:input_type -> transcript.GetDegreeRequest
	11, // 37: transcript.TranscriptService.ListDegrees:input_type -> transcript.ListDegreesRequest
	13, // 38: transcript.TranscriptService.CreateTranscript:input_type -> transcript.CreateTranscriptRequest
	15, // 39: transcript.TranscriptService.GetTranscript:input_type -> transcript.GetTranscriptRequest
	16, // 40: transcript.TranscriptService.AddTranscriptEntry:input_type -> transcript.AddTranscriptEntryRequest
	18, // 41: transcript.TranscriptService.GetTranscriptEntries:input_type -> transcript.GetTranscriptEntriesRequest
	20, // 42: transcript.TranscriptService.UpdateGPA:input_type -> transcript.UpdateGPARequest
	21, // 43: transcript.TranscriptService.CalculateGPA:input_type -> transcript.CalculateGPARequest
	23, // 44: transcript.TranscriptService.CreateStudentDegree:input_type -> transcript.CreateStudentDegreeRequest
	25, // 45: transcript.TranscriptService.GetStudentDegrees:input_type -> transcript.GetStudentDegreesRequest
	27, // 46: transcript.TranscriptService.UpdateDegreeStatus:input_type -> transcript.UpdateDegreeStatusRequest
	28, // 47: transcript.TranscriptService.GenerateTranscriptReport:input_type -> transcript.GenerateTranscriptReportRequest
	9,  // 48: transcript.TranscriptService.CreateDegree:output_type -> transcript.DegreeResponse
	9,  // 49: transcript.TranscriptService.GetDegree:output_type -> transcript.DegreeResponse
	12, // 50: transcript.TranscriptService.ListDegrees:output_type -> transcript.ListDegreesResponse
	14, // 51: transcript.TranscriptService.CreateTranscript:output_type -> transcript.TranscriptResponse
	14, // 52: transcript.TranscriptService.GetTranscript:output_type -> transcript.TranscriptResponse
	17, // 53: transcript.TranscriptService.AddTranscriptEntry:output_type -> transcript.TranscriptEntryResponse
	19, // 54: transcript.TranscriptService.GetTranscriptEntries:output_type -> transcript.GetTranscriptEntriesResponse
	14, // 55: transcript.TranscriptService.UpdateGPA:output_type -> transcript.TranscriptResponse
	22, // 56: transcript.TranscriptService.CalculateGPA:output_type -> transcript.GPAResponse
	24, // 57: transcript.TranscriptService.CreateStudentDegree:output_type -> transcript.StudentDegreeResponse
	26, // 58: transcript.TranscriptService.GetStudentDegrees:output_type -> transcript.GetStudentDegreesResponse
	24, // 59: transcript.TranscriptService.UpdateDegreeStatus:output_type -> transcript.StudentDegreeResponse
	29, // 60: transcript.TranscriptService.GenerateTranscriptReport:output_type -> transcript.TranscriptReport
	48, // [48:61] is the sub-list for method output_type
	35, // [35:48] is the sub-list for method input_type
	35, // [35:35] is the sub-list for extension type_name
	35, // [35:35] is the sub-list for extension extendee
	0,  // [0:35] is the sub-list for field type_name
}

func init() { file_pb_transcript_transcript_proto_init() }
func file_pb_transcript_transcript_proto_init() {
	if File_pb_transcript_transcript_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_pb_transcript_transcript_proto_rawDesc), len(file_pb_transcript_transcript_proto_rawDesc)),
			NumEnums:      4,
			NumMessages:   26,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_pb_transcript_transcript_proto_goTypes,
		DependencyIndexes: file_pb_transcript_transcript_proto_depIdxs,
		EnumInfos:         file_pb_transcript_transcript_proto_enumTypes,
		MessageInfos:      file_pb_transcript_transcript_proto_msgTypes,
	}.Build()
	File_pb_transcript_transcript_proto = out.File
	file_pb_transcript_transcript_proto_goTypes = nil
	file_pb_transcript_transcript_proto_depIdxs = nil
}
